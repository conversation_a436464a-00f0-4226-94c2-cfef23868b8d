import React from 'react';
import { View } from 'react-native';

// Mock Pdf component that renders as a simple View
const MockPdf = React.forwardRef((props, ref) => {
  const {
    source,
    scale = 1,
    minScale = 0.25,
    maxScale = 5,
    horizontal = false,
    page = 1,
    onLoadComplete,
    onPageChanged,
    onScaleChanged,
    onError,
    style,
    enablePaging = true,
    enableRTL = false,
    enableAnnotationRendering = true,
    password = "",
    spacing = 10,
    fitPolicy = 0,
    ...otherProps
  } = props;

  // Simulate PDF loading after component mount
  React.useEffect(() => {
    const timer = setTimeout(() => {
      if (onLoadComplete) {
        // Mock PDF with 3 pages, 800x600 dimensions
        onLoadComplete(3, 'mock-file-path', { width: 800, height: 600 });
      }
      
      if (onPageChanged) {
        onPageChanged(page, 3);
      }
      
      if (onScaleChanged) {
        onScaleChanged(scale);
      }
    }, 100);

    return () => clearTimeout(timer);
  }, [onLoadComplete, onPageChanged, onScaleChanged, page, scale]);

  // Simulate scale changes when scale prop changes
  React.useEffect(() => {
    if (onScaleChanged) {
      onScaleChanged(scale);
    }
  }, [scale, onScaleChanged]);

  // Simulate page changes when page prop changes
  React.useEffect(() => {
    if (onPageChanged) {
      onPageChanged(page, 3);
    }
  }, [page, onPageChanged]);

  // Expose ref methods for testing
  React.useImperativeHandle(ref, () => ({
    // Mock methods that might be called on the PDF component
    setPage: jest.fn((pageNumber) => {
      if (onPageChanged) {
        onPageChanged(pageNumber, 3);
      }
    }),
    setScale: jest.fn((newScale) => {
      if (onScaleChanged) {
        onScaleChanged(newScale);
      }
    }),
  }));

  return (
    <View
      testID="mock-pdf-component"
      style={[
        {
          flex: 1,
          backgroundColor: '#f5f5f5',
          justifyContent: 'center',
          alignItems: 'center',
        },
        style,
      ]}
      {...otherProps}
    />
  );
});

MockPdf.displayName = 'MockPdf';

// Export both default and named exports to match react-native-pdf
module.exports = MockPdf;
module.exports.default = MockPdf;
module.exports.Pdf = MockPdf;

// For ES6 imports
module.exports.__esModule = true;
