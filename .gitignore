# Learn more https://docs.github.com/en/get-started/getting-started-with-git/ignoring-files

# dependencies
node_modules/

# Lockfiles
# Keep `bun.lockb` committed (do NOT ignore bun.lockb)
# npm/yarn/pnpm lockfiles (ignore if you're not using them)
package-lock.json
yarn.lock
pnpm-lock.yaml

# Expo
.expo/
dist/
web-build/
expo-env.d.ts

# Expo cache
.expo-shared/
.expo/cache/
.expo/web/cache/

# Native
*.orig.*
*.jks
*.p8
*.p12
*.key
*.mobileprovision

# iOS
ios/Pods/
ios/DerivedData/
ios/build/
*.xcodeproj/project.xcworkspace/xcshareddata/
*.xcuserstate
*.xcworkspace

# Android
android/.gradle/
android/build/
android/app/build/
android/local.properties
**/gradle-wrapper.jar
.gradle/
*.keystore

# Metro
.metro-health-check*

# Metro / bundler caches
.cache/
.cache-*
metro-cache/
tmp/
temp/

# debug
npm-debug.*
yarn-debug.*
yarn-error.*

# Logs
*.log

# Test coverage
coverage/
jest/cache/
jest-results.json

# macOS
.DS_Store
*.pem

# Editors / IDEs
.vscode/
.idea/
*.sublime-*
*.tmproj

# local env files
.env*.local

# Secrets / credentials
credentials.json
secrets.json
fastlane/report.xml
fastlane/Preview.html


# typescript
*.tsbuildinfo
.vercel

# Generated / build
build/
out/
dist/
.production/
logs/*
