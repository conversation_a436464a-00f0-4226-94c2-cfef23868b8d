{"name": "expo-app", "main": "expo-router/entry", "version": "1.0.0", "expo": {"install": {"exclude": ["react-native@~0.76.6", "react-native-reanimated@~3.16.1", "react-native-gesture-handler@~2.20.0", "react-native-screens@~4.4.0", "react-native-safe-area-context@~4.12.0"]}}, "scripts": {"start": "bunx rork start -p yzhex8i1zsi4v850r2y4n --tunnel", "start-web": "bunx rork start -p yzhex8i1zsi4v850r2y4n --web --tunnel", "start-web-dev": "DEBUG=expo* bunx rork start -p yzhex8i1zsi4v850r2y4n --web --tunnel", "lint": "expo lint", "test": "jest", "build:dev:ios": "bunx eas build --profile development --platform ios", "build:dev:android": "bunx eas build --profile development --platform android", "build:dev:ios-sim": "bunx eas build --profile development-simulator --platform ios", "build:preview:ios": "bunx eas build --profile preview --platform ios", "build:preview:android": "bunx eas build --profile preview --platform android", "build:prod:ios": "bunx eas build --profile production --platform ios", "build:prod:android": "bunx eas build --profile production --platform android", "android": "expo run:android", "ios": "expo run:ios"}, "dependencies": {"@config-plugins/react-native-blob-util": "^12.0.0", "@config-plugins/react-native-pdf": "^12.0.0", "@expo/vector-icons": "~14.0.4", "@nkzw/create-context-hook": "^1.1.0", "@react-native-async-storage/async-storage": "1.23.1", "@react-navigation/native": "^7.1.6", "@stardazed/streams-text-encoding": "^1.0.2", "@tanstack/react-query": "^5.83.0", "@ungap/structured-clone": "^1.3.0", "ajv": "^8.17.1", "expo": "~52.0.27", "expo-blur": "~14.0.3", "expo-constants": "~17.0.8", "expo-document-picker": "~13.0.3", "expo-file-system": "~18.0.12", "expo-font": "~13.0.4", "expo-haptics": "~14.0.1", "expo-image": "~2.0.7", "expo-image-picker": "~16.0.6", "expo-linear-gradient": "~14.0.2", "expo-linking": "~7.0.5", "expo-location": "~18.0.10", "expo-router": "~4.0.21", "expo-sharing": "~13.0.1", "expo-splash-screen": "~0.29.24", "expo-status-bar": "~2.0.1", "expo-symbols": "~0.2.2", "expo-system-ui": "~4.0.9", "expo-updates": "~0.27.4", "expo-web-browser": "~14.0.2", "lucide-react-native": "^0.544.0", "nativewind": "^4.1.23", "react": "18.3.1", "react-dom": "18.3.1", "react-native": "~0.77.1", "react-native-blob-util": "^0.22.2", "react-native-document-scanner-plugin": "^1.0.1", "react-native-gesture-handler": "~2.22.0", "react-native-mlkit-ocr": "^0.3.0", "react-native-pdf": "^6.7.5", "react-native-pdf-from-image": "^0.3.5", "react-native-reanimated": "~3.16.7", "react-native-safe-area-context": "~5.1.0", "react-native-screens": "~4.8.0", "react-native-svg": "15.8.0", "react-native-web": "~0.19.13", "react-native-webview": "13.16.0", "zustand": "^5.0.2"}, "devDependencies": {"@babel/core": "^7.25.2", "@expo/ngrok": "^4.1.0", "@testing-library/jest-native": "^5.4.3", "@testing-library/react-native": "^13.3.3", "@types/jest": "^30.0.0", "@types/react": "~18.3.12", "@types/react-test-renderer": "^19.1.0", "eslint": "^9.31.0", "eslint-config-expo": "~8.0.1", "jest": "^30.1.3", "jest-expo": "~52.0.6", "react-test-renderer": "^18.3.1", "typescript": "~5.8.3"}, "jest": {"preset": "react-native", "setupFilesAfterEnv": ["<rootDir>/jest-setup.js"], "testTimeout": 30000, "moduleNameMapper": {"^@/(.*)$": "<rootDir>/$1"}, "transformIgnorePatterns": ["node_modules/(?!(react-native|@react-native|react-native-.*|@react-navigation/.*|@react-native-async-storage/.*|@testing-library/.*|@nkzw/create-context-hook|expo-.*)/)"], "testMatch": ["**/__tests__/**/*.(test|spec).(ts|tsx|js|jsx)"]}, "private": true}