{"expo": {"name": "AI PDF Assistant", "slug": "ai-pdf-assistant", "version": "1.0.0", "orientation": "portrait", "icon": "./assets/images/icon.png", "scheme": "myapp", "userInterfaceStyle": "automatic", "newArchEnabled": true, "splash": {"image": "./assets/images/splash-icon.png", "resizeMode": "contain", "backgroundColor": "#ffffff"}, "ios": {"supportsTablet": true, "bundleIdentifier": "app.rork.aipdfassistant", "usesIcloudStorage": true, "infoPlist": {"NSPhotoLibraryUsageDescription": "Allow $(PRODUCT_NAME) to access your photos", "NSCameraUsageDescription": "Allow $(PRODUCT_NAME) to access your camera", "NSMicrophoneUsageDescription": "Allow $(PRODUCT_NAME) to access your microphone"}, "runtimeVersion": {"policy": "appVersion"}}, "android": {"adaptiveIcon": {"foregroundImage": "./assets/images/adaptive-icon.png", "backgroundColor": "#ffffff"}, "package": "app.rork.aipdfassistant", "permissions": ["android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.INTERNET", "android.permission.READ_MEDIA_IMAGES", "android.permission.RECORD_AUDIO", "android.permission.CAMERA", "android.permission.READ_EXTERNAL_STORAGE", "android.permission.INTERNET", "android.permission.READ_MEDIA_IMAGES", "android.permission.RECORD_AUDIO"], "runtimeVersion": "1.0.0"}, "web": {"favicon": "./assets/images/favicon.png"}, "plugins": [["expo-router", {"origin": "https://rork.com/"}], ["expo-document-picker", {"iCloudContainerEnvironment": "Production"}], ["expo-image-picker", {"photosPermission": "The app accesses your photos to let you share them with your friends."}], ["react-native-document-scanner-plugin", {"cameraPermission": "Allow $(PRODUCT_NAME) to access your camera for document scanning"}], ["@config-plugins/react-native-pdf"], ["@config-plugins/react-native-blob-util"], "expo-font"], "updates": {"enabled": false, "url": "https://u.expo.dev/766d9980-16aa-47de-8fda-4413175300c4"}, "experiments": {"typedRoutes": true}, "extra": {"router": {"origin": "https://rork.com/"}, "eas": {"projectId": "766d9980-16aa-47de-8fda-4413175300c4"}}}}