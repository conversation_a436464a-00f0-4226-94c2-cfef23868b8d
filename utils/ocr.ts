// Import ML Kit OCR - will be available after package installation
let MlkitOcr: any;
try {
  MlkitOcr = require('react-native-mlkit-ocr').default || require('react-native-mlkit-ocr');
} catch (error) {
  console.warn('[OCR] react-native-mlkit-ocr not available yet. Install the package to enable OCR functionality.');
}

// Import FileSystem for URI normalization
import * as FileSystem from 'expo-file-system';

// Type definitions for ML Kit OCR results
interface MlkitOcrBlock {
  text: string;
  frame: {
    origin: { x: number; y: number };
    size: { width: number; height: number };
  };
  confidence: number;
  cornerPoints: Array<{ x: number; y: number }>;
}

interface MlkitOcrResult {
  blocks: MlkitOcrBlock[];
  width?: number;
  height?: number;
}

// Interface for individual text blocks with bounding box and confidence
export interface OCRBlock {
  text: string;
  boundingBox: {
    top: number;
    left: number;
    width: number;
    height: number;
  };
  confidence: number;
  cornerPoints: Array<{ x: number; y: number }>;
}

// Interface for OCR results from a single page/image
export interface OCRPage {
  fullText: string;
  blocks: OCRBlock[];
  width: number;
  height: number;
}

// Progress reporting interface for OCR processing
export interface OCRProgress {
  completed: number;
  total: number;
  currentImage?: string;
}

// Cancellation token interface
export interface OCRCancellationToken {
  cancelled: boolean;
}

// Configuration for OCR processing
export interface OCROptions {
  confidenceThreshold?: number;
  minBlockSize?: number;
  enableLogging?: boolean;
  concurrency?: number;
  maxErrors?: number;
  onProgress?: (progress: OCRProgress) => void;
  cancelToken?: OCRCancellationToken;
}

// Default OCR configuration
const DEFAULT_OCR_OPTIONS: Required<Omit<OCROptions, 'onProgress' | 'cancelToken'>> = {
  confidenceThreshold: 0.5,
  minBlockSize: 10,
  enableLogging: true,
  concurrency: 2, // Process 2 images at a time
  maxErrors: 3, // Stop if 3 consecutive errors occur
};

/**
 * Performs OCR on a single image URI
 * @param imageUri - The URI of the image to process
 * @param options - OCR processing options
 * @returns Promise<OCRPage> - OCR results for the image
 */
export async function performOcrOnImage(
  imageUri: string,
  options: OCROptions = {}
): Promise<OCRPage> {
  const startTime = Date.now();
  const config = { ...DEFAULT_OCR_OPTIONS, ...options };

  let normalizedUri = imageUri;
  let tempFileCreated = false;
  const tempFiles: string[] = [];

  try {
    if (config.enableLogging) {
      console.log(`[OCR] Starting OCR processing for image: ${imageUri}`);
    }

    // Normalize URI for OCR processing
    if (!imageUri.startsWith('file://') && !imageUri.startsWith('content://')) {
      normalizedUri = await normalizeImageUriForOcr(imageUri);
      tempFileCreated = true;
      tempFiles.push(normalizedUri);
      if (config.enableLogging) {
        console.log(`[OCR] Normalized URI to: ${normalizedUri}`);
      }
    }

    // Perform OCR using ML Kit
    const ocrResult: MlkitOcrResult = await MlkitOcr.detectFromUri(normalizedUri);

    // Validate OCR result structure
    if (!ocrResult || typeof ocrResult !== 'object') {
      throw new Error('OCR library returned invalid result structure');
    }

    // Handle case where blocks is undefined or not an array
    const rawBlocks = ocrResult.blocks;
    if (!Array.isArray(rawBlocks)) {
      if (config.enableLogging) {
        console.warn('[OCR] No text blocks found in image or invalid blocks structure');
      }
      // Return empty OCR result instead of failing
      return {
        fullText: '',
        blocks: [],
        width: ocrResult.width || 0,
        height: ocrResult.height || 0,
      };
    }

    // Convert ML Kit result to our OCRPage format
    const blocks: OCRBlock[] = rawBlocks.map((block) => ({
      text: block.text,
      boundingBox: {
        top: block.frame.origin.y,
        left: block.frame.origin.x,
        width: block.frame.size.width,
        height: block.frame.size.height,
      },
      confidence: block.confidence,
      cornerPoints: block.cornerPoints,
    }));

    // Filter blocks by confidence threshold
    const filteredBlocks = blocks.filter(
      (block) => block.confidence >= config.confidenceThreshold!
    );

    // Filter blocks by minimum size
    const sizeFilteredBlocks = filteredBlocks.filter(
      (block) => block.boundingBox.width * block.boundingBox.height >= config.minBlockSize!
    );

    // Combine all text from blocks
    const fullText = sizeFilteredBlocks.map((block) => block.text).join(' ');

    const ocrPage: OCRPage = {
      fullText,
      blocks: sizeFilteredBlocks,
      width: ocrResult.width || 0,
      height: ocrResult.height || 0,
    };

    if (config.enableLogging) {
      const processingTime = Date.now() - startTime;
      console.log(
        `[OCR] Completed OCR processing in ${processingTime}ms. Found ${sizeFilteredBlocks.length} text blocks.`
      );
    }

    return ocrPage;
  } catch (error) {
    if (config.enableLogging) {
      const processingTime = Date.now() - startTime;
      console.error(`[OCR] Failed OCR processing after ${processingTime}ms:`, error);
    }
    throw new Error(`OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    // Clean up temporary files
    if (tempFileCreated && tempFiles.length > 0) {
      await Promise.all(
        tempFiles.map(async (filePath) => {
          try {
            await FileSystem.deleteAsync(filePath, { idempotent: true });
          } catch (cleanupError) {
            console.warn('[OCR] Failed to clean up temp file:', filePath, cleanupError);
          }
        })
      );
    }
  }
}

/**
 * Performs OCR on multiple images with concurrency control and error handling
 * @param imageUris - Array of image URIs to process
 * @param options - OCR processing options
 * @returns Promise<{ results: OCRPage[], errors: Array<{ index: number, error: string }> }> - OCR results and per-image errors
 */
export async function performOcrOnImages(
  imageUris: string[],
  options: OCROptions = {}
): Promise<{ results: OCRPage[], errors: Array<{ index: number, error: string }> }> {
  const startTime = Date.now();
  const config = { ...DEFAULT_OCR_OPTIONS, ...options };

  // Helper function to check cancellation
  const checkCancellation = () => {
    if (options.cancelToken?.cancelled) {
      throw new Error("OCR processing was cancelled by user");
    }
  };

  // Helper function to report progress
  const reportProgress = (completed: number, currentImage?: string) => {
    if (options.onProgress) {
      try {
        options.onProgress({
          completed,
          total: imageUris.length,
          currentImage
        });
      } catch (error) {
        console.warn('[OCR] Progress callback error:', error);
      }
    }
  };

  if (config.enableLogging) {
    console.log(`[OCR] Starting batch OCR processing for ${imageUris.length} images with concurrency: ${config.concurrency}`);
  }

  // Initial progress report
  reportProgress(0);

  const results: OCRPage[] = [];
  const errors: Array<{ index: number, error: string }> = [];
  let consecutiveErrors = 0;
  let completedCount = 0;

  try {
    // Check for cancellation before starting
    checkCancellation();

    // Process images in batches based on concurrency setting
    for (let i = 0; i < imageUris.length; i += config.concurrency!) {
      // Check for cancellation before each batch
      checkCancellation();

      const batch = imageUris.slice(i, i + config.concurrency!);
      const batchPromises = batch.map(async (uri, batchIndex) => {
        const globalIndex = i + batchIndex;

        try {
          // Check for cancellation before processing each image
          checkCancellation();

          const result = await performOcrOnImage(uri, { ...config, enableLogging: false });
          consecutiveErrors = 0; // Reset consecutive error counter on success

          if (config.enableLogging) {
            console.log(`[OCR] Completed image ${globalIndex + 1}/${imageUris.length}`);
          }

          return { success: true, result, index: globalIndex };
        } catch (error) {
          // Check if error is due to cancellation
          if (error instanceof Error && error.message.includes('cancelled')) {
            throw error; // Re-throw cancellation errors
          }

          consecutiveErrors++;
          const errorMessage = error instanceof Error ? error.message : 'Unknown error';
          if (config.enableLogging) {
            console.warn(`[OCR] Failed to process image ${globalIndex + 1}:`, errorMessage);
          }

          // Check if we should stop due to too many consecutive errors
          if (consecutiveErrors >= config.maxErrors!) {
            if (config.enableLogging) {
              console.warn(`[OCR] Stopping due to ${consecutiveErrors} consecutive errors`);
            }
            return { success: false, error: errorMessage, index: globalIndex };
          }

          return { success: false, error: errorMessage, index: globalIndex };
        }
      });

      const batchResults = await Promise.all(batchPromises);

      // Process batch results and update progress
      for (const batchResult of batchResults) {
        if (batchResult.success) {
          results[batchResult.index] = batchResult.result!;
        } else {
          // Add error to errors array
          errors.push({ index: batchResult.index, error: batchResult.error || 'Unknown error' });

          // Add empty result to maintain array structure
          results[batchResult.index] = {
            fullText: '',
            blocks: [],
            width: 0,
            height: 0,
          } as OCRPage;
        }

        // Update progress after each image (including failed ones)
        completedCount++;
        reportProgress(completedCount, imageUris[batchResult.index]);
      }

      // Early termination if too many consecutive errors
      if (consecutiveErrors >= config.maxErrors!) {
        if (config.enableLogging) {
          console.warn(`[OCR] Early termination after ${consecutiveErrors} consecutive errors`);
        }
        break;
      }
    }

    if (config.enableLogging) {
      const processingTime = Date.now() - startTime;
      const totalBlocks = results.reduce((sum, page) => sum + page.blocks.length, 0);
      const successCount = results.filter(r => r.blocks.length > 0).length;
      console.log(
        `[OCR] Completed batch processing in ${processingTime}ms. ` +
        `Successful: ${successCount}/${imageUris.length}, ` +
        `Total text blocks: ${totalBlocks}, ` +
        `Errors: ${errors.length}`
      );
    }

    return { results, errors };
  } catch (error) {
    const processingTime = Date.now() - startTime;
    const isCancellation = error instanceof Error && error.message.includes('cancelled');

    if (config.enableLogging) {
      if (isCancellation) {
        console.log(`[OCR] Batch OCR processing cancelled after ${processingTime}ms`);
      } else {
        console.error(`[OCR] Batch OCR processing failed after ${processingTime}ms:`, error);
      }
    }

    // Report final progress state
    if (isCancellation) {
      reportProgress(completedCount); // Report partial progress
    }

    if (isCancellation) {
      throw error; // Re-throw cancellation errors as-is
    }

    throw new Error(`Batch OCR processing failed: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Concatenates text from multiple OCR pages
 * @param ocrPages - Array of OCR pages
 * @param separator - Text separator between pages (default: double newline)
 * @returns string - Combined text from all pages
 */
export function concatenateOcrText(
  ocrPages: OCRPage[],
  separator: string = '\n\n'
): string {
  // Handle null, undefined, or non-array input
  if (!ocrPages || !Array.isArray(ocrPages)) {
    console.warn('[OCR] concatenateOcrText received invalid input:', ocrPages);
    return '';
  }

  // Handle empty array
  if (ocrPages.length === 0) {
    return '';
  }

  try {
    return ocrPages
      .filter((page) => page && typeof page === 'object' && typeof page.fullText === 'string')
      .map((page) => page.fullText.trim())
      .filter((text) => text.length > 0)
      .join(separator);
  } catch (error) {
    console.error('[OCR] Error in concatenateOcrText:', error);
    return '';
  }
}

/**
 * Normalizes image URIs for OCR processing
 * @param imageUri - The image URI to normalize
 * @returns Promise<string> - Normalized file:// URI
 */
export async function normalizeImageUriForOcr(imageUri: string): Promise<string> {
  if (!isValidImageUri(imageUri)) {
    throw new Error(`Unsupported image URI format: ${imageUri}`);
  }

  // If already a file:// URI, return as-is
  if (imageUri.startsWith('file://')) {
    return imageUri;
  }

  // For data: URLs, write to temp file
  if (imageUri.startsWith('data:')) {
    const base64Data = imageUri.split(',')[1];
    if (!base64Data) {
      throw new Error('Invalid data URL format');
    }

    const tempDirectory = FileSystem.cacheDirectory;
    if (!tempDirectory) {
      throw new Error('Unable to resolve cache directory for temporary image storage');
    }

    const tempFileName = `ocr_temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
    const tempPath = `${tempDirectory}${tempFileName}`;

    try {
      await FileSystem.writeAsStringAsync(tempPath, base64Data, {
        encoding: FileSystem.EncodingType.Base64,
      });
      return tempPath;
    } catch (error) {
      throw new Error(`Failed to create temp file for data URL: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // For http/https URLs, download to cache
  if (imageUri.startsWith('http://') || imageUri.startsWith('https://')) {
    const tempDirectory = FileSystem.cacheDirectory;
    if (!tempDirectory) {
      throw new Error('Unable to resolve cache directory for image download');
    }

    const tempFileName = `ocr_download_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
    const tempPath = `${tempDirectory}${tempFileName}`;

    try {
      const downloadResult = await FileSystem.downloadAsync(imageUri, tempPath);
      if (downloadResult.status !== 200) {
        throw new Error(`Failed to download image: HTTP ${downloadResult.status}`);
      }
      return downloadResult.uri;
    } catch (error) {
      throw new Error(`Failed to download image: ${error instanceof Error ? error.message : 'Unknown error'}`);
    }
  }

  // For content:// URIs, return as-is (ML Kit should handle these)
  if (imageUri.startsWith('content://')) {
    return imageUri;
  }

  throw new Error(`Unsupported URI format: ${imageUri}`);
}

/**
 * Validates if an image URI is supported by ML Kit OCR
 * @param imageUri - The image URI to validate
 * @returns boolean - True if the URI format is supported
 */
export function isValidImageUri(imageUri: string): boolean {
  // ML Kit OCR supports file://, data:, content://, http://, and https:// URIs
  return (
    imageUri.startsWith('file://') ||
    imageUri.startsWith('data:') ||
    imageUri.startsWith('content://') ||
    imageUri.startsWith('http://') ||
    imageUri.startsWith('https://')
  );
}

/**
 * Gets OCR statistics for a set of OCR pages
 * @param ocrPages - Array of OCR pages
 * @returns Object with statistics about the OCR results
 */
export function getOcrStatistics(ocrPages: OCRPage[]) {
  const totalPages = ocrPages.length;
  const pagesWithText = ocrPages.filter((page) => page.fullText.length > 0).length;
  const totalBlocks = ocrPages.reduce((sum, page) => sum + page.blocks.length, 0);
  const totalCharacters = ocrPages.reduce((sum, page) => sum + page.fullText.length, 0);
  const averageConfidence =
    totalBlocks > 0
      ? ocrPages.reduce(
          (sum, page) =>
            sum + page.blocks.reduce((blockSum, block) => blockSum + block.confidence, 0),
          0
        ) / totalBlocks
      : 0;

  return {
    totalPages,
    pagesWithText,
    totalBlocks,
    totalCharacters,
    averageConfidence: Math.round(averageConfidence * 100) / 100,
    successRate: Math.round((pagesWithText / totalPages) * 100),
  };
}