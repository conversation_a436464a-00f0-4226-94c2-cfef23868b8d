import { Platform } from "react-native";
import * as FileSystem from "expo-file-system";
import { performOcrOnImages, concatenateOcrText, OCRPage, OCROptions } from "./ocr";
import { Document } from "@/hooks/useDocuments";

export function normalizeImageUri(input: string): string {
  if (!input) return input;
  if (input.startsWith('file://') || input.startsWith('data:')) return input;
  if (input.startsWith('/')) return `file://${input}`;
  if (input.startsWith('content://')) return input; // or resolve to temp file via FileSystem
  return input;
}

/**
 * Converts Android absolute paths to file URIs that Expo FileSystem can handle
 * @param path - The path to convert (can be absolute path or file URI)
 * @returns string - Properly formatted file URI
 */
export function normalizeAndroidPath(path: string): string {
  if (!path) return path;

  // Already a file URI, return as-is
  if (path.startsWith('file://')) return path;

  // Android external storage paths need special handling
  if (Platform.OS === 'android' && path.startsWith('/storage/emulated/0/')) {
    // Convert to file URI format
    return `file://${path}`;
  }

  // Android app-specific paths
  if (Platform.OS === 'android' && path.startsWith('/data/user/0/')) {
    return `file://${path}`;
  }

  // Other absolute paths
  if (path.startsWith('/')) {
    return `file://${path}`;
  }

  return path;
}

/**
 * Attempts to verify file existence using multiple path formats for Android compatibility
 * @param originalPath - The original path returned by the library
 * @param expectedPath - The path we expected the file to be at
 * @returns Promise<{exists: boolean, actualPath: string, fileInfo?: any}>
 */
export async function verifyFileExistsAndroid(originalPath: string, expectedPath?: string): Promise<{
  exists: boolean;
  actualPath: string;
  fileInfo?: any;
}> {
  const pathsToTry = [originalPath];

  // Add normalized versions
  if (originalPath !== normalizeAndroidPath(originalPath)) {
    pathsToTry.push(normalizeAndroidPath(originalPath));
  }

  // Add expected path if different
  if (expectedPath && expectedPath !== originalPath) {
    pathsToTry.push(expectedPath);
    if (expectedPath !== normalizeAndroidPath(expectedPath)) {
      pathsToTry.push(normalizeAndroidPath(expectedPath));
    }
  }

  // Try each path format
  for (const pathToTry of pathsToTry) {
    try {
      console.log(`[File Verification] Checking path: ${pathToTry}`);
      const fileInfo = await FileSystem.getInfoAsync(pathToTry);
      if (fileInfo.exists) {
        console.log(`[File Verification] File found at: ${pathToTry} (${fileInfo.size} bytes)`);
        return {
          exists: true,
          actualPath: pathToTry,
          fileInfo
        };
      }
    } catch (error) {
      console.log(`[File Verification] Failed to check ${pathToTry}:`, error instanceof Error ? error.message : 'Unknown error');
    }
  }

  return {
    exists: false,
    actualPath: originalPath
  };
}

// Import PDF generation - will be available after package installation
let createPdf: any;
try {
  createPdf = require("react-native-pdf-from-image").createPdf;
} catch (error) {
  console.warn("[PDF] react-native-pdf-from-image not available yet. Install the package to enable PDF generation.");
}

function assertPdfModuleAvailable() {
  if (typeof createPdf !== 'function') {
    throw new Error("PDF generation module is not available. Install 'react-native-pdf-from-image' to enable PDF creation.");
  }
}

export async function readPdfAsBase64(uri: string): Promise<string> {
  if (!uri) throw new Error("Empty URI");

  if (Platform.OS === 'web') {
    if (uri.startsWith('data:application/pdf;base64,')) {
      return uri.split(',')[1];
    }
    if (uri.startsWith('data:')) {
      // Unexpected data URL
      const [, base64 = ''] = uri.split(',');
      return base64;
    }
    if (uri.startsWith('blob:')) {
      const response = await fetch(uri);
      const blob = await response.blob();
      const base64DataUrl = await new Promise<string>((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(blob);
      });
      const [, base64 = ''] = base64DataUrl.split(',');
      return base64;
    }
    throw new Error('Unsupported web URI format for PDF');
  }

  // Native: read as base64 from file URI
  const base64 = await FileSystem.readAsStringAsync(uri, {
    encoding: FileSystem.EncodingType.Base64,
  });
  return base64;
}

export function isLargeFile(size: number, thresholdMB?: number): boolean {
  if (!size || !Number.isFinite(size)) return false;

  // Platform-specific thresholds for native PDF rendering
  let defaultThreshold = 20; // Default 20MB
  if (Platform.OS === 'ios' || Platform.OS === 'android') {
    defaultThreshold = 50; // Native PDF viewer can handle larger files better
  }

  const threshold = thresholdMB ?? defaultThreshold;
  return size / (1024 * 1024) > threshold;
}

export function getPdfFileName(): string {
  const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
  return `document_${timestamp}.pdf`;
}

export function getScannedDocumentName(): string {
  try {
    // Try locale-specific date first
    const localeDate = new Date().toLocaleDateString()?.replace(/\//g, '-');
    if (localeDate && localeDate !== 'Invalid Date') {
      return `Scanned_Document_${localeDate}.pdf`;
    }
  } catch (error) {
    console.warn('[Files] Locale date formatting failed:', error);
  }

  // Fallback to ISO date format
  const isoDate = new Date().toISOString().split('T')[0];
  return `Scanned_Document_${isoDate}.pdf`;
}

export function validateImageFormat(uri: string): { isValid: boolean; format?: string; error?: string } {
  if (!uri || typeof uri !== 'string') {
    return { isValid: false, error: 'Invalid URI: empty or not a string' };
  }

  // Check for supported formats
  const supportedFormats = ['.jpg', '.jpeg', '.png', '.heic', '.webp'];
  const dataUrlFormats = ['data:image/jpeg', 'data:image/png', 'data:image/webp'];
  const unsupportedFormats = ['.pdf', '.mp4', '.avi', '.mov', '.txt', '.doc'];

  // Handle data URLs
  if (uri.startsWith('data:')) {
    const isSupported = dataUrlFormats.some(format => uri.startsWith(format));
    if (isSupported) {
      const format = uri.split(';')[0].replace('data:image/', '');
      return { isValid: true, format };
    }
    return { isValid: false, error: 'Unsupported data URL format' };
  }

  // Handle file URIs and paths
  const lowerUri = uri.toLowerCase();

  // Check for explicitly unsupported formats first
  const unsupportedFormat = unsupportedFormats.find(format => lowerUri.includes(format));
  if (unsupportedFormat) {
    return { isValid: false, error: `Unsupported file format: ${unsupportedFormat}` };
  }

  // Check for supported formats
  const supportedFormat = supportedFormats.find(format => lowerUri.includes(format));
  if (supportedFormat) {
    return { isValid: true, format: supportedFormat.replace('.', '') };
  }

  // Check for generic image indicators (content:// URIs from Android)
  if (uri.startsWith('content://') && lowerUri.includes('image')) {
    return { isValid: true, format: 'unknown' }; // Let the native library handle it
  }

  // For file:// URIs without clear extension, be more restrictive
  if (uri.startsWith('file://')) {
    return { isValid: false, error: 'File URI must have a supported image extension' };
  }

  return { isValid: false, error: `Unsupported image format. Supported: ${supportedFormats.join(', ')}` };
}

/**
 * Converts base64 PDF data to a blob URI for react-native-pdf
 * @param base64Data - Base64 encoded PDF data (without data: prefix)
 * @returns Promise<string> - Blob URI that can be used with react-native-pdf
 */
export async function convertBase64ToBlobUri(base64Data: string): Promise<string> {
  if (!base64Data) {
    throw new Error("Empty base64 data provided");
  }

  // Remove data URL prefix if present
  const cleanBase64 = base64Data.replace(/^data:application\/pdf;base64,/, '');

  if (Platform.OS === 'web') {
    // For web, create a blob URL
    const binaryString = atob(cleanBase64);
    const bytes = new Uint8Array(binaryString.length);
    for (let i = 0; i < binaryString.length; i++) {
      bytes[i] = binaryString.charCodeAt(i);
    }
    const blob = new Blob([bytes], { type: 'application/pdf' });
    return URL.createObjectURL(blob);
  }

  // For native platforms, write to cache directory and return file URI
  const cacheDirectory = FileSystem.cacheDirectory;
  if (!cacheDirectory) {
    throw new Error('Cache directory is not available on this platform');
  }

  const fileName = `temp_pdf_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.pdf`;
  const filePath = `${cacheDirectory}${fileName}`;

  try {
    await FileSystem.writeAsStringAsync(filePath, cleanBase64, {
      encoding: FileSystem.EncodingType.Base64,
    });

    // Verify the file was created and get its info
    const fileInfo = await FileSystem.getInfoAsync(filePath);
    console.log('[Files] Base64 PDF converted to file URI:', filePath);
    console.log('[Files] File info:', { exists: fileInfo.exists, size: fileInfo.exists ? (fileInfo as any).size : 'N/A' });

    if (!fileInfo.exists) {
      throw new Error('File was not created successfully');
    }

    return filePath;
  } catch (error) {
    console.error('[Files] Failed to convert base64 to file URI:', error);
    throw new Error(`Failed to convert PDF data: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Prepares PDF source for native PDF viewer
 * @param source - PDF source with various URI formats
 * @returns Promise<string> - URI compatible with react-native-pdf
 */
export async function preparePdfSourceForNative(source: string): Promise<string> {
  if (!source) {
    throw new Error("Empty PDF source provided");
  }

  // If it's already a file URI, return as-is
  if (source.startsWith('file://')) {
    return source;
  }

  // If it's a data URL, convert to blob/file URI
  if (source.startsWith('data:application/pdf;base64,')) {
    return await convertBase64ToBlobUri(source);
  }

  // If it's base64 without prefix, add prefix and convert
  if (!source.includes('://') && !source.startsWith('/')) {
    return await convertBase64ToBlobUri(source);
  }

  // For other URI schemes, return as-is and let react-native-pdf handle it
  return source;
}

export async function generatePdfFromImages(
  imageUris: string[],
  options: {
    nameOverride?: string;
    paperSize?: 'A4' | 'A3' | 'LETTER' | 'LEGAL';
    customPaperSize?: { width: number; height: number };
  } = {}
): Promise<string> {
  if (!imageUris || imageUris.length === 0) {
    throw new Error("No images provided for PDF generation");
  }

  // Validate image URIs
  const invalidUris = imageUris.filter(uri => !uri || typeof uri !== 'string');
  if (invalidUris.length > 0) {
    throw new Error(`Invalid image URIs detected: ${invalidUris.length} out of ${imageUris.length} URIs are invalid`);
  }

  // Validate image formats
  const formatValidation = imageUris.map((uri, index) => ({
    index,
    uri,
    validation: validateImageFormat(uri)
  }));

  const unsupportedImages = formatValidation.filter(item => !item.validation.isValid);
  if (unsupportedImages.length > 0) {
    const errorDetails = unsupportedImages.map(item =>
      `Image ${item.index + 1}: ${item.validation.error}`
    ).join('; ');
    throw new Error(`Unsupported image formats detected: ${errorDetails}`);
  }

  console.log(`[PDF Generation] Processing ${imageUris.length} images with formats:`,
    formatValidation.map(item => item.validation.format).join(', '));

  assertPdfModuleAvailable();

  const outputDirectory = FileSystem.documentDirectory ?? FileSystem.cacheDirectory;
  if (!outputDirectory) {
    throw new Error('File system directory is not available for PDF generation on this platform.');
  }

  const temporaryFiles: string[] = [];

  try {
    // Generate a unique filename for the PDF
    const { nameOverride, paperSize = 'A4', customPaperSize } = options;
    let pdfFileName = nameOverride || getPdfFileName();
    // Ensure .pdf extension is enforced
    if (!pdfFileName.toLowerCase().endsWith('.pdf')) {
      pdfFileName += '.pdf';
    }
    const pdfPath = `${outputDirectory}${pdfFileName}`;

    // Convert image URIs to absolute file paths that the PDF library requires
    const absoluteImagePaths = await Promise.all(
      imageUris.map(async (uri) => {
        const normalized = normalizeImageUri(uri);

        if (normalized.startsWith('file://')) {
          return normalized;
        }

        if (normalized.startsWith('data:')) {
          const base64Data = normalized.split(',')[1];
          const tempDirectory = FileSystem.cacheDirectory ?? FileSystem.documentDirectory;
          if (!tempDirectory) {
            throw new Error('Unable to resolve a writable directory for temporary image storage.');
          }

          const tempFileName = `temp_image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
          const tempPath = `${tempDirectory}${tempFileName}`;
          await FileSystem.writeAsStringAsync(tempPath, base64Data, {
            encoding: FileSystem.EncodingType.Base64,
          });
          temporaryFiles.push(tempPath);
          return tempPath;
        }

        // Android content provider URI: copy to a temp file
        if (normalized.startsWith('content://')) {
          // Copy content:// URI to temp file
          const tempDirectory = FileSystem.cacheDirectory ?? FileSystem.documentDirectory;
          if (!tempDirectory) {
            throw new Error('Unable to resolve a writable directory for temporary image storage.');
          }
          const tempFileName = `temp_image_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
          const tempPath = `${tempDirectory}${tempFileName}`;
          await FileSystem.copyAsync({ from: normalized, to: tempPath });
          temporaryFiles.push(tempPath);
          return tempPath;
        }

        // Web blob URI: fetch and write to a temp file as base64
        if (normalized.startsWith('blob:')) {
          const tempDirectory = FileSystem.cacheDirectory ?? FileSystem.documentDirectory;
          if (!tempDirectory) {
            throw new Error('Unable to resolve a writable directory for temporary image storage.');
          }

          const tempFileName = `temp_blob_${Date.now()}_${Math.random().toString(36).substr(2, 9)}.jpg`;
          const tempPath = `${tempDirectory}${tempFileName}`;

          const response = await fetch(normalized);
          const blob = await response.blob();
          const arrayBuffer = await blob.arrayBuffer();
          const base64Data = btoa(String.fromCharCode(...new Uint8Array(arrayBuffer)));

          await FileSystem.writeAsStringAsync(tempPath, base64Data, {
            encoding: FileSystem.EncodingType.Base64,
          });
          temporaryFiles.push(tempPath);
          return tempPath;
        }

        // At this point, the URI scheme is unsupported
        throw new Error(`Unsupported image URI format: ${uri}. Supported: file://, data:, blob:, content://, or absolute path`);
      })
    );

    // Generate PDF from images with customizable paper size
    // Extract the filename without path for the library's name parameter
    const libraryPdfName = pdfPath.split('/').pop() || 'document.pdf';

    const pdfOptions: any = {
      imagePaths: absoluteImagePaths, // Library expects 'imagePaths', not 'images'
      name: libraryPdfName, // Library expects 'name', not 'outputPath'
      paperSize: paperSize,
    };

    if (customPaperSize) {
      pdfOptions.customPaperSize = customPaperSize;
    }

    const result = await createPdf(pdfOptions);

    // The library returns an object with filePath, but we need to return our expected path
    // For consistency with our existing code, we'll return the original pdfPath
    console.log(`[PDF Generation] Library returned:`, result);

    // CRITICAL FIX: Check if the library actually created the file at the expected path
    // If not, we need to handle the path mismatch
    let finalPdfPath = pdfPath;

    if (result && typeof result === 'object' && result.filePath) {
      const libraryCreatedPath = result.filePath;
      console.log(`[PDF Generation] Library created file at: ${libraryCreatedPath}`);
      console.log(`[PDF Generation] Expected file at: ${pdfPath}`);

      // If the library created the file at a different path, we need to use that path
      if (libraryCreatedPath !== pdfPath) {
        console.warn(`[PDF Generation] Path mismatch detected. Using library path: ${libraryCreatedPath}`);
        finalPdfPath = libraryCreatedPath;
      }
    }

    // Verify the PDF file was created using Android-compatible verification
    let fileInfo;
    try {
      console.log(`[PDF Generation] Verifying PDF file creation...`);
      console.log(`[PDF Generation] Final path: ${finalPdfPath}`);
      console.log(`[PDF Generation] Expected path: ${pdfPath}`);

      // Use Android-compatible file verification
      const verificationResult = await verifyFileExistsAndroid(finalPdfPath, pdfPath);

      if (verificationResult.exists) {
        console.log(`[PDF Generation] PDF file verified: ${verificationResult.actualPath} (${verificationResult.fileInfo?.size || 'unknown size'} bytes)`);
        fileInfo = verificationResult.fileInfo;
        finalPdfPath = verificationResult.actualPath;
      } else {
        // If Android verification fails, try one more fallback with the original expected path
        if (pdfPath !== finalPdfPath) {
          console.log(`[PDF Generation] Android verification failed, trying original expected path: ${pdfPath}`);
          try {
            const fallbackFileInfo = await FileSystem.getInfoAsync(pdfPath);
            if (fallbackFileInfo.exists) {
              console.log(`[PDF Generation] PDF file found at original expected path: ${pdfPath} (${fallbackFileInfo.size} bytes)`);
              fileInfo = fallbackFileInfo;
              finalPdfPath = pdfPath;
            } else {
              throw new Error(`Generated PDF file does not exist at any verified path. Tried: ${finalPdfPath}, ${pdfPath}`);
            }
          } catch (fallbackError) {
            throw new Error(`Generated PDF file does not exist at any path. Primary verification failed, fallback also failed: ${fallbackError instanceof Error ? fallbackError.message : 'Unknown error'}`);
          }
        } else {
          throw new Error(`Generated PDF file does not exist at path: ${finalPdfPath}`);
        }
      }
    } catch (fileError) {
      console.error('[PDF Generation] Failed to verify PDF file:', fileError);
      throw new Error(`Generated PDF file verification failed: ${fileError instanceof Error ? fileError.message : 'Unknown error'}`);
    }

    return finalPdfPath;
  } catch (error) {
    console.error('PDF generation failed:', error);
    throw new Error(`Failed to generate PDF: ${error instanceof Error ? error.message : 'Unknown error'}`);
  } finally {
    // Clean up temporary files if any were created
    await Promise.all(
      temporaryFiles.map(async (path) => {
        try {
          await FileSystem.deleteAsync(path, { idempotent: true });
        } catch (cleanupError) {
          console.warn('Failed to clean up temporary file:', path, cleanupError);
        }
      })
    );
  }
}

// Progress reporting interface
export interface DocumentCreationProgress {
  step: string;
  percentage: number;
  currentItem?: number;
  totalItems?: number;
}

// Cancellation token interface
export interface CancellationToken {
  cancelled: boolean;
}

/**
 * Creates a document with both PDF generation and OCR processing
 * @param imageUris - Array of image URIs to process
 * @param options - Options for OCR processing, document creation, progress reporting, and cancellation
 * @returns Promise<Document> - Complete document with PDF and OCR data
 */
export async function createDocumentWithOcr(
  imageUris: string[],
  options: {
    skipOcr?: boolean;
    ocrOptions?: OCROptions;
    documentName?: string;
    onProgress?: (progress: DocumentCreationProgress) => void;
    cancelToken?: CancellationToken;
  } = {}
): Promise<Document> {
  const startTime = Date.now();
  const { skipOcr = true, ocrOptions = {}, documentName, onProgress, cancelToken } = options;

  if (!imageUris || imageUris.length === 0) {
    throw new Error("No images provided for document creation");
  }

  // Helper function to check cancellation
  const checkCancellation = () => {
    if (cancelToken?.cancelled) {
      throw new Error("Document creation was cancelled by user");
    }
  };

  // Helper function to report progress
  const reportProgress = (step: string, percentage: number, currentItem?: number, totalItems?: number) => {
    if (onProgress) {
      try {
        onProgress({ step, percentage, currentItem, totalItems });
      } catch (error) {
        console.warn('[Document Creation] Progress callback error:', error);
      }
    }
  };

  console.log(`[Document Creation] Starting document creation with ${imageUris.length} images`);
  reportProgress("Initializing document creation", 0);

  let pdfPath: string = '';
  let ocrPages: OCRPage[] = [];
  let ocrText = '';
  let ocrProcessed = false;
  let ocrEdited = false;

  try {
    // Check for cancellation before starting
    checkCancellation();

    // Step 1: Generate PDF from images
    reportProgress("Generating PDF from images", 10);
    const pdfStartTime = Date.now();
    pdfPath = await generatePdfFromImages(imageUris, { nameOverride: documentName });
    const pdfGenerationTime = Date.now() - pdfStartTime;
    console.log(`[Document Creation] PDF generated in ${pdfGenerationTime}ms`);

    // Check for cancellation after PDF generation
    checkCancellation();
    reportProgress("PDF generation complete", skipOcr ? 80 : 30);

    // Step 2: Perform OCR processing (if not skipped)
    if (!skipOcr) {
      try {
        reportProgress("Starting text extraction", 35);
        const ocrStartTime = Date.now();

        // Create progress callback for OCR processing
        const ocrProgressCallback = (progress: { completed: number; total: number; currentImage?: string }) => {
          const ocrProgressPercentage = 35 + Math.round((progress.completed / progress.total) * 45); // 35% to 80%
          reportProgress(
            `Extracting text from page ${progress.completed + 1} of ${progress.total}`,
            ocrProgressPercentage,
            progress.completed + 1,
            progress.total
          );
        };

        const ocrResult = await performOcrOnImages(imageUris, {
          ...ocrOptions,
          onProgress: ocrProgressCallback,
          cancelToken: cancelToken
        });

        ocrPages = ocrResult.results || [];
        if (ocrResult.errors && ocrResult.errors.length > 0 && ocrOptions.enableLogging !== false) {
          console.warn(`[Document Creation] OCR completed with ${ocrResult.errors.length} errors:`, ocrResult.errors);
        }

        // Check if OCR results are valid
        if (!ocrResult.results || !Array.isArray(ocrResult.results)) {
          console.warn('[Document Creation] OCR returned invalid results, using empty text');
          ocrText = '';
          ocrProcessed = false;
        } else {
          // Safely concatenate OCR text with error handling
          try {
            ocrText = concatenateOcrText(ocrPages);
            ocrProcessed = true;
          } catch (concatError) {
            console.warn('[Document Creation] Failed to concatenate OCR text, using empty text:', concatError);
            ocrText = '';
            ocrProcessed = false;
          }
        }
        const ocrProcessingTime = Date.now() - ocrStartTime;
        console.log(`[Document Creation] OCR processed in ${ocrProcessingTime}ms`);
        reportProgress("Text extraction complete", 80);
      } catch (ocrError) {
        // Check if error is due to cancellation
        if (ocrError instanceof Error && ocrError.message.includes('cancelled')) {
          throw ocrError; // Re-throw cancellation errors
        }
        console.warn('[Document Creation] OCR processing failed, continuing with PDF only:', ocrError);
        reportProgress("Text extraction failed, continuing with PDF only", 80);
        // OCR failed but PDF succeeded - continue with PDF-only document
      }
    }

    // Check for cancellation before finalizing
    checkCancellation();

    // Step 3: Get PDF file info and verify it exists using Android-compatible verification
    reportProgress("Finalizing document", 85);
    let fileInfo;
    let finalPdfPath = pdfPath;
    try {
      console.log(`[Document Creation] Verifying PDF file: ${pdfPath}`);

      // Use Android-compatible file verification
      const verificationResult = await verifyFileExistsAndroid(pdfPath);

      if (verificationResult.exists) {
        console.log(`[Document Creation] PDF file verified: ${verificationResult.actualPath} (${verificationResult.fileInfo?.size || 'unknown size'} bytes)`);
        fileInfo = verificationResult.fileInfo;
        finalPdfPath = verificationResult.actualPath;
      } else {
        throw new Error(`Generated PDF file does not exist at path: ${pdfPath}`);
      }
    } catch (fileError) {
      console.error('[Document Creation] Failed to verify PDF file:', fileError);
      throw new Error(`Generated PDF file verification failed: ${fileError instanceof Error ? fileError.message : 'Unknown error'}`);
    }

    // Step 4: Create document object
    reportProgress("Creating document metadata", 90);
    const finalDocumentName = documentName || getPdfFileName();
    const documentId = `doc_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;

    const document: Document = {
      id: documentId,
      name: finalDocumentName,
      uri: finalPdfPath, // Use the verified path instead of original pdfPath
      size: fileInfo.size || 0,
      createdAt: new Date().toISOString(),
      isScanned: true,
      pageCount: imageUris.length,
      originalImages: imageUris,
      // OCR data
      ocrText,
      ocrPages,
      ocrProcessed,
      ocrEdited,
    };

    const totalTime = Date.now() - startTime;
    console.log(`[Document Creation] Document created successfully in ${totalTime}ms`);

    if (ocrProcessed && ocrText) {
      console.log(`[Document Creation] Document includes OCR data: ${ocrText.length} characters extracted`);
    }

    reportProgress("Document creation complete", 100);
    return document;
  } catch (error) {
    const totalTime = Date.now() - startTime;
    const isCancellation = error instanceof Error && error.message.includes('cancelled');

    if (isCancellation) {
      console.log(`[Document Creation] Document creation cancelled after ${totalTime}ms`);
      reportProgress("Document creation cancelled", 0);
    } else {
      console.error(`[Document Creation] Document creation failed after ${totalTime}ms:`, error);
      reportProgress("Document creation failed", 0);
    }

    // Clean up PDF if it was created but an error occurred later
    if (pdfPath && await FileSystem.getInfoAsync(pdfPath).then(info => info.exists).catch(() => false)) {
      try {
        await FileSystem.deleteAsync(pdfPath, { idempotent: true });
        console.log('[Document Creation] Cleaned up PDF file after error');
      } catch (cleanupError) {
        console.warn('[Document Creation] Failed to clean up PDF file:', cleanupError);
      }
    }

    if (isCancellation) {
      throw error; // Re-throw cancellation errors as-is
    }

    throw new Error(`Failed to create document with OCR: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
}

/**
 * Updates OCR text in an existing document
 * @param documentId - ID of the document to update
 * @param newOcrText - New OCR text content
 * @returns Promise<Document> - Updated document
 */
export async function updateDocumentOcrText(
  documentId: string,
  newOcrText: string
): Promise<Document> {
  if (!documentId?.trim()) {
    throw new Error("Invalid document ID");
  }

  if (!newOcrText) {
    throw new Error("OCR text cannot be empty");
  }

  try {
    // This would typically be handled by the useDocuments hook
    // For now, we'll throw an error indicating this should be handled by the hook
    throw new Error("Use the updateDocument function from useDocuments hook to update OCR text");
  } catch (error) {
    console.error('[Document Update] Failed to update OCR text:', error);
    throw error;
  }
}

