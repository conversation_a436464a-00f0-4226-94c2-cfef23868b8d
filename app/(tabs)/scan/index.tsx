import React, { useState, useRef, useEffect } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Alert,
  ScrollView,
  Platform,
  Switch,
  ActivityIndicator,
  Modal,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Camera, Image as ImageIcon, FileText, Plus, X, Settings } from "lucide-react-native";
import * as ImagePicker from "expo-image-picker";
import * as FileSystem from "expo-file-system";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { router } from "expo-router";
import { useDocuments } from "@/hooks/useDocuments";
import { createDocumentWithOcr, DocumentCreationProgress, CancellationToken, getScannedDocumentName } from "@/utils/files";

// Processing state interface
interface ProcessingState {
  status: 'idle' | 'scanning' | 'generating-pdf' | 'processing-ocr' | 'complete';
  progress: number;
  canCancel: boolean;
  step?: string;
  currentItem?: number;
  totalItems?: number;
}

// OCR skip preference key
const OCR_SKIP_PREFERENCE_KEY = '@scan_skip_ocr';

export default function ScanScreen() {
  const { addDocument } = useDocuments();

  // Enhanced processing state
  const [processingState, setProcessingState] = useState<ProcessingState>({
    status: 'idle',
    progress: 0,
    canCancel: false,
  });

  // OCR skip option state - default to true for faster PDF generation
  const [skipOcr, setSkipOcr] = useState(true);
  const [showSettings, setShowSettings] = useState(false);

  // Cancellation token
  const cancelTokenRef = useRef<CancellationToken>({ cancelled: false });

  // Load OCR skip preference on mount
  useEffect(() => {
    const loadOcrPreference = async () => {
      try {
        const savedPreference = await AsyncStorage.getItem(OCR_SKIP_PREFERENCE_KEY);
        if (savedPreference !== null) {
          setSkipOcr(JSON.parse(savedPreference));
        }
      } catch (error) {
        console.warn('Failed to load OCR preference:', error);
      }
    };
    loadOcrPreference();
  }, []);

  // Save OCR skip preference
  const saveOcrPreference = async (value: boolean) => {
    try {
      await AsyncStorage.setItem(OCR_SKIP_PREFERENCE_KEY, JSON.stringify(value));
      setSkipOcr(value);
    } catch (error) {
      console.warn('Failed to save OCR preference:', error);
      setSkipOcr(value); // Still update state even if save fails
    }
  };

  // Progress callback for document creation
  const handleProgress = (progress: DocumentCreationProgress) => {
    setProcessingState(prev => ({
      ...prev,
      progress: progress.percentage,
      step: progress.step,
      currentItem: progress.currentItem,
      totalItems: progress.totalItems,
    }));
  };

  // Cancel processing
  const handleCancel = () => {
    Alert.alert(
      'Cancel Processing',
      'Are you sure you want to cancel document processing?',
      [
        { text: 'Continue', style: 'cancel' },
        {
          text: 'Cancel',
          style: 'destructive',
          onPress: () => {
            cancelTokenRef.current.cancelled = true;
            setProcessingState(prev => ({ ...prev, canCancel: false }));
          },
        },
      ]
    );
  };

  // Reset processing state
  const resetProcessingState = () => {
    setProcessingState({
      status: 'idle',
      progress: 0,
      canCancel: false,
    });
    cancelTokenRef.current = { cancelled: false };
  };

  const handleCameraScanning = async () => {
    let DocumentScanner: any;
    try { DocumentScanner = require('react-native-document-scanner-plugin').default; } catch {}
    if (!DocumentScanner?.scanDocument) {
      Alert.alert('Not supported on Web', 'Document scanning is available on iOS/Android only.');
      return;
    }

    // Reset cancellation token and set initial state
    resetProcessingState();
    setProcessingState({
      status: 'scanning',
      progress: 5,
      canCancel: false,
      step: 'Opening camera scanner...',
    });

    try {
      const { scannedImages, status } = await DocumentScanner.scanDocument({
        maxNumDocuments: 10,
        responseType: 'imageFilePath',
        croppedImageQuality: 100,
      });

      if (status === 'cancel') {
        // user cancelled the scan, don't show error
        resetProcessingState();
        return;
      }

      if (!scannedImages || scannedImages.length === 0) {
        resetProcessingState();
        Alert.alert('No pages detected', 'Please try scanning again.');
        return;
      }

      // Update state for document processing
      setProcessingState({
        status: 'generating-pdf',
        progress: 10,
        canCancel: true,
        step: 'Processing scanned images...',
      });

      // Create document with OCR processing
      const document = await createDocumentWithOcr(scannedImages, {
        documentName: getScannedDocumentName(),
        skipOcr: skipOcr,
        onProgress: handleProgress,
        cancelToken: cancelTokenRef.current,
      });

      await addDocument(document);

      // Clean up temporary scanned images
      try {
        for (const imagePath of scannedImages) {
          await FileSystem.deleteAsync(imagePath, { idempotent: true });
        }
      } catch (cleanupError) {
        // Ignore cleanup failures
        console.warn('Failed to cleanup temporary images:', cleanupError);
      }

      // Set completion state
      setProcessingState({
        status: 'complete',
        progress: 100,
        canCancel: false,
        step: 'Document created successfully',
      });

      const pageCount = scannedImages.length;
      const ocrMessage = document.ocrProcessed ? " and text extracted" : "";
      const skipMessage = skipOcr ? " (text extraction skipped)" : "";
      const successMessage = pageCount === 1
        ? `Document has been scanned and converted to PDF${ocrMessage}${skipMessage}`
        : `Document has been created with ${pageCount} pages${ocrMessage}${skipMessage}`;

      Alert.alert(
        "Success",
        successMessage,
        [
          {
            text: "View Document",
            onPress: () => {
              resetProcessingState();
              router.push({
                pathname: "/(tabs)/documents/viewer",
                params: { documentId: document.id },
              });
            },
          },
          {
            text: "OK",
            onPress: () => resetProcessingState(),
          },
        ]
      );
    } catch (error) {
      console.error("Document scanning error:", error);

      const isCancellation = error instanceof Error && error.message.includes('cancelled');

      if (isCancellation) {
        setProcessingState({
          status: 'idle',
          progress: 0,
          canCancel: false,
          step: 'Processing cancelled',
        });

        Alert.alert(
          "Cancelled",
          "Document processing was cancelled.",
          [{ text: "OK", onPress: () => resetProcessingState() }]
        );
      } else {
        resetProcessingState();
        Alert.alert(
          "Error",
          `Failed to scan document: ${error instanceof Error ? error.message : 'Unknown error'}`,
          [{ text: "OK" }]
        );
      }
    }
  };

  const handleImagePicker = async () => {
    // Check if running on web platform
    if (Platform.OS === 'web') {
      Alert.alert('Not supported on Web', 'PDF generation from photo library is not supported on web yet.');
      return;
    }

    const { status } = await ImagePicker.requestMediaLibraryPermissionsAsync();
    if (status !== "granted") {
      Alert.alert("Permission needed", "Photo library permission is required");
      return;
    }

    try {
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'], // Updated to use array format instead of enum
        quality: 1,
        allowsMultipleSelection: true,
      });

      if (!result.canceled && result.assets.length > 0) {
        // Reset cancellation token and set initial state
        resetProcessingState();
        setProcessingState({
          status: 'generating-pdf',
          progress: 10,
          canCancel: true,
          step: 'Processing selected images...',
        });

        try {
          // Create document with OCR processing
          const document = await createDocumentWithOcr(
            result.assets.map(asset => asset.uri),
            {
              documentName: getScannedDocumentName(),
              skipOcr: skipOcr,
              onProgress: handleProgress,
              cancelToken: cancelTokenRef.current,
            }
          );

          await addDocument(document);

          // Set completion state
          setProcessingState({
            status: 'complete',
            progress: 100,
            canCancel: false,
            step: 'Document created successfully',
          });

          const pageCount = result.assets.length;
          const ocrMessage = document.ocrProcessed ? " and text extracted" : "";
          const skipMessage = skipOcr ? " (text extraction skipped)" : "";
          const successMessage = pageCount === 1
            ? `Document has been scanned and converted to PDF${ocrMessage}${skipMessage}`
            : `Document has been created with ${pageCount} pages${ocrMessage}${skipMessage}`;

          Alert.alert(
            "Success",
            successMessage,
            [
              {
                text: "View Document",
                onPress: () => {
                  resetProcessingState();
                  router.push({
                    pathname: "/(tabs)/documents/viewer",
                    params: { documentId: document.id },
                  });
                },
              },
              {
                text: "OK",
                onPress: () => resetProcessingState(),
              },
            ]
          );
        } catch (error) {
          console.error("Document creation error:", error);

          const isCancellation = error instanceof Error && error.message.includes('cancelled');

          if (isCancellation) {
            setProcessingState({
              status: 'idle',
              progress: 0,
              canCancel: false,
              step: 'Processing cancelled',
            });

            Alert.alert(
              "Cancelled",
              "Document processing was cancelled.",
              [{ text: "OK", onPress: () => resetProcessingState() }]
            );
          } else {
            resetProcessingState();
            Alert.alert(
              "Error",
              `Failed to create document: ${error instanceof Error ? error.message : 'Unknown error'}`,
              [{ text: "OK" }]
            );
          }
        }
      }
    } catch (error) {
      resetProcessingState();
      console.error("Image picker error:", error);
    }
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView style={styles.content}>
        <View style={styles.header}>
          <View style={styles.titleRow}>
            <View style={styles.titleContainer}>
              <Text style={styles.title}>Scan Documents</Text>
              <Text style={styles.subtitle}>
                Convert physical documents to PDF using your camera or photo library
              </Text>
            </View>
            <TouchableOpacity
              style={styles.settingsButton}
              onPress={() => setShowSettings(true)}
              disabled={processingState.status !== 'idle'}
              testID="settings-button"
            >
              <Settings size={24} />
            </TouchableOpacity>
          </View>
        </View>

        <View style={styles.scanOptions}>
          <TouchableOpacity
            style={[
              styles.scanOption,
              processingState.status !== 'idle' && styles.scanOptionDisabled
            ]}
            onPress={handleCameraScanning}
            disabled={processingState.status !== 'idle'}
          >
            <View style={styles.scanIconContainer}>
              <Camera size={32} />
            </View>
            <Text style={styles.scanOptionTitle}>Camera Scanner</Text>
            <Text style={styles.scanOptionDescription}>
              Advanced document scanning with auto edge detection and multi-page support
            </Text>
          </TouchableOpacity>

          <TouchableOpacity
            style={[
              styles.scanOption,
              (processingState.status !== 'idle') && styles.scanOptionDisabled,
              Platform.OS === 'web' && styles.disabledOption,
            ]}
            onPress={handleImagePicker}
            disabled={processingState.status !== 'idle' || Platform.OS === 'web'}
          >
            <View style={[styles.scanIconContainer, Platform.OS === 'web' && styles.disabledIconContainer]}>
              <ImageIcon size={32} />
            </View>
            <Text style={[styles.scanOptionTitle, Platform.OS === 'web' && styles.disabledText]}>Photo Library</Text>
            <Text style={[styles.scanOptionDescription, Platform.OS === 'web' && styles.disabledText]}>
              {Platform.OS === 'web' ? 'PDF generation not supported on web yet' : 'Select images from your photo library to convert'}
            </Text>
          </TouchableOpacity>
        </View>

        {/* OCR Toggle - Prominent placement for easy access */}
        <View style={styles.ocrToggleContainer}>
          <View style={styles.ocrToggleCard}>
            <View style={styles.ocrToggleInfo}>
              <Text style={styles.ocrToggleTitle}>Text Extraction (OCR)</Text>
              <Text style={styles.ocrToggleDescription}>
                {skipOcr
                  ? 'Disabled - Fast PDF creation (~700ms)'
                  : 'Enabled - Slower but searchable text (~3000ms)'
                }
              </Text>
            </View>
            <Switch
              value={!skipOcr} // Inverted because we want "Enable OCR" not "Skip OCR"
              onValueChange={(value) => saveOcrPreference(!value)}
              trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
              thumbColor={'#FFFFFF'}
              disabled={processingState.status !== 'idle'}
              testID="ocr-toggle-switch"
              accessibilityRole="switch"
              accessibilityLabel={`OCR text extraction ${!skipOcr ? 'enabled' : 'disabled'}`}
              accessibilityHint="Toggle to enable or disable text extraction from documents"
            />
          </View>
        </View>

        {processingState.status !== 'idle' && (
          <View style={styles.processingContainer}>
            <View style={styles.processingCard}>
              <View style={styles.processingHeader}>
                <FileText size={48} />
                {processingState.canCancel && (
                  <TouchableOpacity
                    accessibilityRole="button"
                    accessibilityLabel="Cancel processing"
                    testID="cancel-processing"
                    style={styles.cancelButton}
                    onPress={handleCancel}
                  >
                    <X size={20} />
                  </TouchableOpacity>
                )}
              </View>

              <Text style={styles.processingTitle}>
                {processingState.status === 'scanning' && 'Opening Scanner'}
                {processingState.status === 'generating-pdf' && 'Creating PDF'}
                {processingState.status === 'processing-ocr' && 'Extracting Text'}
                {processingState.status === 'complete' && 'Complete'}
              </Text>

              <Text style={styles.processingDescription}>
                {processingState.step || 'Processing document...'}
              </Text>

              {processingState.currentItem && processingState.totalItems && (
                <Text style={styles.processingProgress}>
                  Page {processingState.currentItem} of {processingState.totalItems}
                </Text>
              )}

              <View style={styles.progressBarContainer}>
                <View style={styles.progressBarBackground}>
                  <View
                    style={[
                      styles.progressBarFill,
                      { width: `${processingState.progress}%` }
                    ]}
                  />
                </View>
                <Text style={styles.progressText}>
                  {Math.round(processingState.progress)}%
                </Text>
              </View>

              <ActivityIndicator size="large" color="#3B82F6" style={styles.spinner} />
            </View>
          </View>
        )}

        <View style={styles.featuresContainer}>
          <Text style={styles.featuresTitle}>Features</Text>
          
          <View style={styles.featuresList}>
            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Plus size={16} />
              </View>
              <Text style={styles.featureText}>Auto edge detection</Text>
            </View>

            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Plus size={16} />
              </View>
              <Text style={styles.featureText}>Perspective correction</Text>
            </View>

            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Plus size={16} />
              </View>
              <Text style={styles.featureText}>Image enhancement</Text>
            </View>

            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Plus size={16} />
              </View>
              <Text style={styles.featureText}>Multi-page PDF creation</Text>
            </View>

            <View style={styles.featureItem}>
              <View style={styles.featureIcon}>
                <Plus size={16} />
              </View>
              <Text style={styles.featureText}>Text recognition and extraction</Text>
            </View>
          </View>
        </View>
      </ScrollView>

      {/* Settings Modal */}
      <Modal
        visible={showSettings}
        transparent
        animationType="slide"
        onRequestClose={() => setShowSettings(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.modalContainer}>
            <View style={styles.modalHeader}>
              <Text style={styles.modalTitle}>Scan Settings</Text>
              <TouchableOpacity
                style={styles.modalCloseButton}
                onPress={() => setShowSettings(false)}
              >
                <X size={24} />
              </TouchableOpacity>
            </View>

            <View style={styles.modalContent}>
              <View style={styles.settingItem}>
                <View style={styles.settingInfo}>
                  <Text style={styles.settingTitle}>Skip Text Extraction (OCR)</Text>
                  <Text style={styles.settingDescription}>
                    Skip OCR processing for faster document creation (~700ms vs ~3000ms). PDFs are fully functional for viewing and sharing, but text won't be searchable or extractable.
                  </Text>
                </View>
                <Switch
                  value={skipOcr}
                  onValueChange={saveOcrPreference}
                  trackColor={{ false: '#E5E7EB', true: '#3B82F6' }}
                  thumbColor={skipOcr ? '#FFFFFF' : '#FFFFFF'}
                />
              </View>
            </View>

            <View style={styles.modalFooter}>
              <TouchableOpacity
                style={styles.modalButton}
                onPress={() => setShowSettings(false)}
              >
                <Text style={styles.modalButtonText}>Done</Text>
              </TouchableOpacity>
            </View>
          </View>
        </View>
      </Modal>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F9FAFB",
  },
  content: {
    flex: 1,
  },
  header: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  titleRow: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  titleContainer: {
    flex: 1,
    marginRight: 16,
  },
  title: {
    fontSize: 28,
    fontWeight: "700",
    color: "#1F2937",
    marginBottom: 8,
  },
  settingsButton: {
    padding: 8,
    borderRadius: 8,
    backgroundColor: '#FFFFFF',
    borderWidth: 1,
    borderColor: '#E5E7EB',
  },
  subtitle: {
    fontSize: 16,
    color: "#6B7280",
    lineHeight: 24,
  },
  scanOptions: {
    paddingHorizontal: 20,
    gap: 16,
  },
  scanOption: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 24,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  scanIconContainer: {
    width: 80,
    height: 80,
    backgroundColor: "#F0F9FF",
    borderRadius: 20,
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 16,
  },
  scanOptionTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 8,
  },
  scanOptionDescription: {
    fontSize: 14,
    color: "#6B7280",
    textAlign: "center",
    lineHeight: 20,
  },
  scanOptionDisabled: {
    opacity: 0.5,
  },
  // OCR Toggle styles
  ocrToggleContainer: {
    paddingHorizontal: 20,
    paddingTop: 16,
  },
  ocrToggleCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 16,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  ocrToggleInfo: {
    flex: 1,
    marginRight: 16,
  },
  ocrToggleTitle: {
    fontSize: 16,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 4,
  },
  ocrToggleDescription: {
    fontSize: 14,
    color: "#6B7280",
    lineHeight: 18,
  },
  processingContainer: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  processingCard: {
    backgroundColor: "#FFFFFF",
    borderRadius: 16,
    padding: 32,
    alignItems: "center",
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  processingTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#1F2937",
    marginTop: 16,
    marginBottom: 8,
  },
  processingDescription: {
    fontSize: 14,
    color: "#6B7280",
    textAlign: "center",
    marginBottom: 8,
  },
  processingHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
    position: 'relative',
  },
  cancelButton: {
    position: 'absolute',
    right: 0,
    top: 0,
    padding: 8,
    borderRadius: 20,
    backgroundColor: '#FEF2F2',
    borderWidth: 1,
    borderColor: '#FECACA',
  },
  processingProgress: {
    fontSize: 12,
    color: "#9CA3AF",
    textAlign: "center",
    marginBottom: 16,
  },
  progressBarContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    width: '100%',
    marginBottom: 16,
  },
  progressBarBackground: {
    flex: 1,
    height: 8,
    backgroundColor: '#E5E7EB',
    borderRadius: 4,
    marginRight: 12,
  },
  progressBarFill: {
    height: '100%',
    backgroundColor: '#3B82F6',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 12,
    fontWeight: '600',
    color: '#374151',
    minWidth: 35,
  },
  spinner: {
    marginTop: 8,
  },
  featuresContainer: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  featuresTitle: {
    fontSize: 20,
    fontWeight: "600",
    color: "#1F2937",
    marginBottom: 16,
  },
  featuresList: {
    backgroundColor: "#FFFFFF",
    borderRadius: 12,
    padding: 20,
    borderWidth: 1,
    borderColor: "#E5E7EB",
  },
  featureItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 8,
  },
  featureIcon: {
    width: 24,
    height: 24,
    backgroundColor: "#EFF6FF",
    borderRadius: 6,
    alignItems: "center",
    justifyContent: "center",
    marginRight: 12,
  },
  featureText: {
    fontSize: 16,
    color: "#374151",
  },
  // Web-disabled styles
  disabledOption: {
    opacity: 0.6,
    backgroundColor: "#F3F4F6",
  },
  disabledIconContainer: {
    backgroundColor: "#E5E7EB",
  },
  disabledText: {
    color: "#9CA3AF",
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  modalContainer: {
    backgroundColor: '#FFFFFF',
    borderTopLeftRadius: 20,
    borderTopRightRadius: 20,
    paddingBottom: 34, // Safe area padding
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 20,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: '#E5E7EB',
  },
  modalTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
  },
  modalCloseButton: {
    padding: 4,
  },
  modalContent: {
    paddingHorizontal: 20,
    paddingVertical: 24,
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    justifyContent: 'space-between',
  },
  settingInfo: {
    flex: 1,
    marginRight: 16,
  },
  settingTitle: {
    fontSize: 16,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 4,
  },
  settingDescription: {
    fontSize: 14,
    color: '#6B7280',
    lineHeight: 20,
  },
  modalFooter: {
    paddingHorizontal: 20,
    paddingTop: 16,
    borderTopWidth: 1,
    borderTopColor: '#E5E7EB',
  },
  modalButton: {
    backgroundColor: '#3B82F6',
    borderRadius: 12,
    paddingVertical: 16,
    alignItems: 'center',
  },
  modalButtonText: {
    fontSize: 16,
    fontWeight: '600',
    color: '#FFFFFF',
  },
});