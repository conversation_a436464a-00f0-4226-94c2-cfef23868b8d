# Rork AI PDF Assistant — Codebase Summary

This document describes the product scope (PRD), architecture, key modules and methods, and guiding principles for this Expo Router + React Native project.

## 1) PRD — Product Requirements

- Goal: A cross‑platform (iOS, Android, Web) app to manage PDFs and scanned documents, preview them, and chat with an AI assistant about a selected document.
- Users can:
  - Add/import PDF files from device storage (Document Picker).
  - Scan physical pages into a “PDF-like” document via camera or photos (demo stores images with a scanned flag).
  - View PDFs or scanned images with zoom/rotate/fullscreen.
  - Delete documents from the local library.
  - Start an AI chat, optionally scoped to a chosen document (demo prompts; does not parse file content).
- Storage & Sync: Local device storage using AsyncStorage (via a provider). No backend persistence.
- Non‑goals (for this template): OCR, real PDF generation from images, server storage, auth, full‑text search.

## 2) Architecture Overview

- Framework: Expo (managed) + Expo Router (file-based routing) + React Native + TypeScript.
- Navigation:
  - Root stack: `app/_layout.tsx` with a single child: `(tabs)`.
  - Tabs: Documents, Scan, Chat.
  - Nested stacks per feature for screen-specific headers and presentation.
- State & Data:
  - React Query handles the in-memory cache and access to local storage (AsyncStorage) via custom provider.
  - `hooks/useDocuments.ts` encapsulates document CRUD (add/delete/list) with React Query mutations/queries.
- Persistence:
  - `providers/storage.tsx` wraps AsyncStorage with safe helpers exposed through a context hook.
- Files & Media:
  - Add/import PDFs via `expo-document-picker` (web uses Data URL for persistence).
  - Scan/capture via `expo-image-picker` and `expo-camera` (stored as image data, flagged `isScanned`).
- Viewer:
  - **Platform-Aware Architecture**: Uses native PDF viewer on iOS/Android, WebView fallback for web/images.
  - **iOS/Android**: Native PDF rendering via `react-native-pdf` for better performance and stability.
  - **Web Platform**: Shows unsupported message for PDFs (native viewer not available).
  - **Image Sources**: Uses WebView with HTML wrapper for consistent zoom/rotate UX across platforms.
  - **OCR Text Overlay**: Enhanced coordinate mapping system works with both native and WebView implementations.
  - **Backward Compatibility**: Zero breaking changes - identical API maintained across all implementations.
- AI Chat:
  - Simple POST to `https://toolkit.rork.com/text/llm/` with a contextual system prompt. No document content ingestion.

## 3) Project Structure

- app/
  - `_layout.tsx` — Root providers (React Query, Storage), splash hide, root stack.
  - `index.tsx` — Redirects to `/(tabs)/documents` and applies safe area padding.
  - `+not-found.tsx` — 404 route.
  - `(tabs)/_layout.tsx` — Tab bar configuration (Documents, Scan, Chat).
  - `(tabs)/documents/_layout.tsx` — Documents stack (list + modal viewer).
  - `(tabs)/documents/index.tsx` — Documents list, add/delete/view/test PDF.
  - `(tabs)/documents/viewer.tsx` — PDF/image viewer with zoom/rotate/fullscreen.
  - `(tabs)/scan/_layout.tsx` — Scan stack (index + full-screen camera).
  - `(tabs)/scan/index.tsx` — Camera or photo library import; demo conversion to “PDF-like”.
  - `(tabs)/scan/camera.tsx` — Camera capture flow and save.
  - `(tabs)/chat/_layout.tsx` — Chat stack with large title.
  - `(tabs)/chat/index.tsx` — AI chat UI and request logic.
- hooks/
  - `useDocuments.ts` — React Query wrapper over Storage for documents.
- providers/
  - `storage.tsx` — Context provider exposing `setItem/getItem/removeItem` using AsyncStorage.
- assets/ — Icons and images referenced in `app.json`.
- Configuration: `app.json`, `package.json`, `tsconfig.json`, `eslint.config.js`.
- Docs for contributors: `.github/copilot-instructions.md`, `.github/ts-expo.md`.

## 4) Data Model

- Document (hooks/useDocuments.ts)
  - `id: string`
  - `name: string`
  - `uri: string` (PDF base64 data URL on web, or file URI on native; scanned images stored as data URLs/URIs with `isScanned`)
  - `size: number`
  - `createdAt: string` (ISO)
  - `isScanned?: boolean`

## 5) Key Files & Methods

- app/_layout.tsx
  - `RootLayout()` — Sets up SplashScreen handling, wraps app with `QueryClientProvider`, `StorageProvider`, `GestureHandlerRootView`, and renders the root Stack with tabs.

- app/index.tsx
  - `HomeScreen()` — Applies safe area padding and `Redirect`s to `/(tabs)/documents`.

- app/(tabs)/_layout.tsx
  - `TabLayout()` — Tab bar configuration and icons for Documents, Scan, Chat.

- app/(tabs)/documents/_layout.tsx
  - `DocumentsLayout()` — Stack options for Documents screens; modal presentation for `viewer`.

- app/(tabs)/documents/index.tsx
  - `createTestPDF()` — Generates a base64-embedded sample PDF and saves it, then opens the viewer.
  - `handleAddDocument()` — Launches Document Picker, converts blob to data URL on web, saves document via `useDocuments`.
  - `handleDeleteDocument(id, name)` — Confirms and removes a document.
  - `handleViewDocument(document)` — Navigates to `viewer` with `documentId`.
  - `formatFileSize(bytes)` — Human-readable file size display.

- app/(tabs)/documents/viewer.tsx
  - `PDFViewerScreen()` — Viewer UI and controls; delegates rendering to `components/pdf/PDFViewer` (PDF.js).
  - `loadPDFContent(doc)` — Loads selected document into base64; supports:
    - Web: data URL PDFs, images, blob→data URL conversion.
    - Native: file read via `expo-file-system`.
  - `handleChatWithDocument()` — Navigates to Chat tab with current document context.
  - `handleDeleteDocument()` — Confirms deletion and navigates back.
  - `handleZoomIn/handleZoomOut/handleRotate()` — Updates local state and forwards commands to `PDFViewer` via ref.
  - `toggleFullscreen()` — Switches between embedded and fullscreen WebView.
  - `testPDFViewer()` — Adds debug information and basic base64 validity checks.
  - (Replaced) `getDocumentViewerHTML` — Legacy HTML builder removed in favor of PDF.js component.

- app/(tabs)/scan/index.tsx
  - `handleCameraScanning()` — Requests camera permission and navigates to `camera` screen.
  - `handleImagePicker()` — Requests photo permission, lets user select image(s), simulates conversion, saves a scanned doc, and offers to open viewer.

- app/(tabs)/scan/camera.tsx
  - `toggleCameraFacing()` — Switches front/back camera.
  - `takePicture()` — Captures image via `CameraView`.
  - `retakePicture()` — Resets capture state.
  - `processPicture()` — Converts captured image to data URL on web; saves as scanned doc; optionally navigates to viewer.

- app/(tabs)/chat/index.tsx
  - `ChatScreen()` — Simple chat UI, supports optional `documentId` context.
  - `sendMessage()` — Pushes user message, builds a contextual system prompt, calls `https://toolkit.rork.com/text/llm/`, and appends AI response.

- hooks/useDocuments.ts
  - `useDocuments()` — Returns `{ documents, isLoading, error, refetch, addDocument, deleteDocument }` using React Query and `useStorage`.
  - Internals:
    - `DOCUMENTS_KEY` — Storage key for persistence.
    - `documentsQuery` — Loads and caches documents from AsyncStorage.
    - `addDocumentMutation/deleteDocumentMutation` — Persist mutations + cache update.

- providers/storage.tsx
  - `StorageProvider` — Context provider.
  - `useStorage()` — Returns `{ setItem, getItem, removeItem }` with basic input guards and AsyncStorage integration.

## 6) Navigation & Routing

- Root stack (`app/_layout.tsx`) holds a single group: `(tabs)`.
- Tabs are defined in `app/(tabs)/_layout.tsx` with iconography from `lucide-react-native`.
- Each feature (documents, scan, chat) owns a nested stack to manage headers and presentation (modal, full screen).

## 7) Persistence & React Query

- Storage is centralized in `providers/storage.tsx` for safety and testability.
- `useDocuments` composes storage and React Query queries/mutations.
- Web specifics:
  - Blob URIs from pickers are converted to data URLs (base64) for persistence compatibility.

## 8) Styling & UI

- Inline StyleSheet objects with Tailwind-like color semantics (e.g., `#3B82F6`).
- Safe area handled in root/home and camera header.
- Icons via `lucide-react-native`.
- PDF/image rendering via WebView (HTML template strings for UI and debugging overlays).

## 9) Permissions & Platform Notes

- iOS/Android permissions declared in `app.json` for Camera, Photos, Microphone, Storage, Internet.
- On Web, Blob → Base64 conversion is required for stored files.
- Scanned documents are stored as image data URLs with `isScanned: true`; viewer adapts rendering path.

## 10) Scripts & Tooling

- Package manager: Bun.
- Scripts (`package.json`):
  - `start` — Rork + Expo dev server with tunnel.
  - `start-web` — Web preview with tunnel.
  - `start-web-dev` — Web preview with debug logs.
  - `lint`, `test` — Lint and Jest (jest-expo preset).
- Notable dependencies: `expo-router`, `@tanstack/react-query`, `react-native-webview`, `expo-document-picker`, `expo-camera`, `expo-image-picker`, `expo-file-system`.

## 11) Principles & Conventions

From the codebase and `.github` docs:
- TypeScript-first; strict mode enabled via `tsconfig.json`.
- Feature isolation under `app/(tabs)/<feature>` with `_layout.tsx` per feature.
- Use Expo Router for file-based navigation; prefer declarative stacks/tabs.
- Shared logic in `hooks/` and shared state/providers in `providers/`.
- Keep components functional; avoid classes. Prefer small, composable helpers.
- Naming: descriptive variable names (e.g., `isLoading`, `hasError`).
- Error-first handling with early returns; platform-conditional logic where required.
- Use React Query for cache updates after mutations.
- Respect safe areas and platform differences (web/native URIs, permissions).
- Bun for all scripts and dependency management.

## 12) Extension Ideas (Future)

- Real PDF generation/merging from images (e.g., native modules or web libs).
- OCR for scanned pages and semantic chat grounded in extracted text.
- Full-text search, tagging, folders, and cloud sync.
- Authentication and remote storage (Supabase/Firebase/custom API).
- Improved viewer with multi-page thumbnails and annotations.

## 13) How To Run

- Install: `bun i`
- Web preview: `bun run start-web`
- Native preview: `bun run start` (then press `i` for iOS)
- components/pdf/PDFViewer.tsx
  - `PDFViewer` — Platform-aware facade component. Props: `source: { kind: 'pdf' | 'image'; data }`, `initialZoom`, `rotation`.
  - **iOS/Android + PDF**: Delegates to `NativePDF` component using `react-native-pdf`.
  - **Web + PDF**: Shows unsupported platform message.
  - **Any platform + Image**: Uses WebView with HTML template.
  - Methods via ref: `zoomIn()`, `zoomOut()`, `fitWidth()`, `rotate(deg)` - forwarded to appropriate implementation.

- components/pdf/NativePDF.tsx
  - `NativePDF` — Native PDF viewer wrapper for iOS/Android using `react-native-pdf`.
  - Handles base64 to blob URI conversion for native PDF library compatibility.
  - Generates page metrics compatible with `TextOverlay` coordinate mapping.
  - Implements identical API to `PDFViewer` for seamless integration.

- components/pdf/TextOverlay.tsx
  - `TextOverlay` — OCR text overlay with enhanced coordinate mapping.
  - **Enhanced Compatibility**: Works with both native PDF and WebView metrics.
  - **Coordinate Mapping**: `mapBlockToOverlayRect()` handles different metric sources and rotation.
  - **Platform Detection**: Automatically adapts to native vs WebView coordinate systems.
