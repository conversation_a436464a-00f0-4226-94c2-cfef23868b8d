import React, { useState, useMemo, useCallback } from 'react';
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Dimensions,
  Modal,
  ScrollView,
} from 'react-native';
import { OCRPage, OCRBlock } from '@/utils/ocr';

interface TextOverlayProps {
  ocrPages: OCRPage[];
  currentPage: number;
  zoom: number;
  rotation: number;
  visible: boolean;
  onToggleVisibility?: () => void;
  style?: any;
  pageMetrics?: Map<number, any>; // Page metrics from PDFViewer
  containerLayout?: { x: number; y: number; width: number; height: number } | null;
}

interface TooltipData {
  text: string;
  confidence: number;
  position: { x: number; y: number };
}

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

// Pure function for coordinate transformation - exported for testing
export function mapBlockToOverlayRect(
  block: OCRBlock,
  ocrPage: OCRPage,
  pageMetrics: any,
  zoom: number,
  rotation: number,
  containerLayout?: { x: number; y: number; width: number; height: number } | null
): { x: number; y: number; width: number; height: number } {
  try {
    const { boundingBox } = block;

    // Validate input data
    if (!boundingBox || typeof boundingBox.left !== 'number' || typeof boundingBox.top !== 'number' ||
        typeof boundingBox.width !== 'number' || typeof boundingBox.height !== 'number') {
      console.warn('[TextOverlay] Invalid bounding box data:', boundingBox);
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    if (!ocrPage) {
      console.warn('[TextOverlay] No OCR page data available');
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    // Base coordinates from OCR
    let x = boundingBox.left;
    let y = boundingBox.top;
    let width = boundingBox.width;
    let height = boundingBox.height;

    // Calculate proper scale factors with enhanced native PDF support
    let scaleX = 1;
    let scaleY = 1;

    if (pageMetrics && ocrPage.width > 0 && ocrPage.height > 0) {
      // Enhanced coordinate mapping for native PDF viewer
      // Check if we have naturalWidth/Height from native PDF viewer
      const naturalWidth = pageMetrics.naturalWidth || pageMetrics.width;
      const naturalHeight = pageMetrics.naturalHeight || pageMetrics.height;

      if (naturalWidth && naturalHeight) {
        // Use natural dimensions for more accurate scaling
        scaleX = pageMetrics.width / ocrPage.width;
        scaleY = pageMetrics.height / ocrPage.height;
      } else {
        // Fallback to rendered dimensions
        scaleX = pageMetrics.width / ocrPage.width;
        scaleY = pageMetrics.height / ocrPage.height;
      }

      // Add validation for scale factors
      if (!Number.isFinite(scaleX) || scaleX <= 0) {
        console.warn('[TextOverlay] Invalid scaleX, falling back to zoom:', scaleX);
        scaleX = zoom;
      }
      if (!Number.isFinite(scaleY) || scaleY <= 0) {
        console.warn('[TextOverlay] Invalid scaleY, falling back to zoom:', scaleY);
        scaleY = zoom;
      }
    } else if (pageMetrics) {
      // Fallback: use zoom factor if OCR dimensions are invalid
      scaleX = zoom;
      scaleY = zoom;
    } else {
      // Final fallback: use zoom factor
      scaleX = zoom;
      scaleY = zoom;
    }

    // Apply scaling
    x *= scaleX;
    y *= scaleY;
    width *= scaleX;
    height *= scaleY;

    // Add page offset to position relative to viewport
    if (pageMetrics) {
      x += pageMetrics.offsetX || 0;
      y += pageMetrics.offsetY || 0;
    }

    // Compensate for container layout and WebView coordinate space differences
    if (containerLayout) {
      // Subtract container offsets to align with WebView coordinate space
      x -= containerLayout.x;
      y -= containerLayout.y;

      // Apply device pixel ratio compensation if needed
      const devicePixelRatio = typeof window !== 'undefined' ? window.devicePixelRatio || 1 : 1;
      if (devicePixelRatio !== 1) {
        x /= devicePixelRatio;
        y /= devicePixelRatio;
        width /= devicePixelRatio;
        height /= devicePixelRatio;
      }
    }

    // Apply rotation transformation around the page center
    if (rotation !== 0 && pageMetrics) {
      // Enhanced rotation handling for native PDF viewer
      // Native PDF viewers may handle rotation differently than WebView
      const isNativePDF = pageMetrics.naturalWidth !== undefined;

      // Handle width/height swapping for 90°/270° rotations
      if (rotation % 180 !== 0) {
        // For native PDF viewer, rotation is typically handled internally
        // For WebView PDF.js, we may need to swap dimensions
        if (!isNativePDF) {
          // Only apply dimension swapping for WebView implementation
          const tempWidth = width;
          const tempHeight = height;
          width = tempHeight;
          height = tempWidth;
        }
      }

      const pageCenterX = (pageMetrics.offsetX || 0) + pageMetrics.width / 2;
      const pageCenterY = (pageMetrics.offsetY || 0) + pageMetrics.height / 2;

      const radians = (rotation * Math.PI) / 180;

      // Calculate block center relative to page center
      const blockCenterX = x + width / 2;
      const blockCenterY = y + height / 2;

      // Rotate block center around page center
      const rotatedCenterX = pageCenterX +
        (blockCenterX - pageCenterX) * Math.cos(radians) -
        (blockCenterY - pageCenterY) * Math.sin(radians);
      const rotatedCenterY = pageCenterY +
        (blockCenterX - pageCenterX) * Math.sin(radians) +
        (blockCenterY - pageCenterY) * Math.cos(radians);

      // Update position based on rotated center
      x = rotatedCenterX - width / 2;
      y = rotatedCenterY - height / 2;
    }

    // Ensure coordinates are finite numbers
    if (!isFinite(x) || !isFinite(y) || !isFinite(width) || !isFinite(height)) {
      console.warn('[TextOverlay] Invalid transformed coordinates:', { x, y, width, height });
      return { x: 0, y: 0, width: 0, height: 0 };
    }

    return { x, y, width, height };
  } catch (error) {
    console.error('[TextOverlay] Error transforming coordinates:', error);
    return { x: 0, y: 0, width: 0, height: 0 };
  }
}

export default function TextOverlay({
  ocrPages,
  currentPage,
  zoom,
  rotation,
  visible,
  onToggleVisibility,
  style,
  pageMetrics,
  containerLayout,
}: TextOverlayProps) {
  const [selectedBlock, setSelectedBlock] = useState<TooltipData | null>(null);
  const [tooltipVisible, setTooltipVisible] = useState(false);

  // Memoized page metrics to prevent unnecessary re-renders
  const memoizedPageMetrics = useMemo(() => {
    const metrics = pageMetrics?.get(currentPage);
    if (!metrics) return null;

    // Enhanced hash for native PDF viewer compatibility
    const hash = `${metrics.width}-${metrics.height}-${metrics.offsetX || 0}-${metrics.offsetY || 0}-${metrics.rotation || 0}-${metrics.scale || 1}-${metrics.naturalWidth || 0}-${metrics.naturalHeight || 0}`;
    return { ...metrics, _hash: hash };
  }, [pageMetrics, currentPage]);

  // Get current page OCR data
  const currentPageData = useMemo(() => {
    if (!ocrPages || currentPage < 0 || currentPage >= ocrPages.length) {
      return null;
    }
    return ocrPages[currentPage];
  }, [ocrPages, currentPage]);

  // Transform coordinates based on zoom, rotation, and page metrics with proper scaling
  const transformCoordinates = useMemo(() => {
    return (block: OCRBlock) => {
      if (!currentPageData) {
        return { x: 0, y: 0, width: 0, height: 0 };
      }

      return mapBlockToOverlayRect(
        block,
        currentPageData,
        memoizedPageMetrics,
        zoom,
        rotation,
        containerLayout
      );
    };
  }, [zoom, rotation, currentPage, memoizedPageMetrics, currentPageData, containerLayout]);

  // Get confidence-based styling
  const getBlockStyle = useMemo(() => {
    return (confidence: number) => {
      let backgroundColor = 'rgba(59, 130, 246, 0.3)'; // Default blue
      let borderColor = 'rgba(59, 130, 246, 0.8)';

      if (confidence >= 0.9) {
        backgroundColor = 'rgba(34, 197, 94, 0.3)'; // High confidence - green
        borderColor = 'rgba(34, 197, 94, 0.8)';
      } else if (confidence >= 0.7) {
        backgroundColor = 'rgba(251, 191, 36, 0.3)'; // Medium confidence - yellow
        borderColor = 'rgba(251, 191, 36, 0.8)';
      } else {
        backgroundColor = 'rgba(239, 68, 68, 0.3)'; // Low confidence - red
        borderColor = 'rgba(239, 68, 68, 0.8)';
      }

      return {
        backgroundColor,
        borderColor,
        borderWidth: 1,
      };
    };
  }, []);

  const handleBlockPress = (block: OCRBlock, position: { x: number; y: number }) => {
    const tooltipData: TooltipData = {
      text: block.text,
      confidence: block.confidence,
      position,
    };

    setSelectedBlock(tooltipData);
    setTooltipVisible(true);
  };

  const closeTooltip = () => {
    setTooltipVisible(false);
    setSelectedBlock(null);
  };

  if (!visible || !currentPageData || currentPageData.blocks.length === 0) {
    return null;
  }

  return (
    <>
      <View style={[styles.container, style]} pointerEvents={visible ? "auto" : "none"}>
        {currentPageData.blocks.map((block, index) => {
          const transformedCoords = transformCoordinates(block);
          const blockStyle = getBlockStyle(block.confidence);

          return (
            <TouchableOpacity
              key={index}
              testID={`ocr-block-${index}`}
              style={[
                styles.textBlock,
                {
                  left: transformedCoords.x,
                  top: transformedCoords.y,
                  width: transformedCoords.width,
                  height: transformedCoords.height,
                  ...blockStyle,
                },
              ]}
              onPress={() => handleBlockPress(block, transformedCoords)}
              activeOpacity={0.7}
            >
              <Text style={styles.blockText} numberOfLines={1}>
                {block.text}
              </Text>
            </TouchableOpacity>
          );
        })}
      </View>

      {/* Tooltip Modal */}
      <Modal
        visible={tooltipVisible}
        transparent
        animationType="fade"
        onRequestClose={closeTooltip}
      >
        <TouchableOpacity style={styles.modalOverlay} onPress={closeTooltip} activeOpacity={1}>
          <View style={styles.tooltipContainer}>
            {selectedBlock && (
              <ScrollView style={styles.tooltipContent}>
                <Text style={styles.tooltipTitle}>Recognized Text</Text>
                <Text style={styles.tooltipText}>{selectedBlock.text}</Text>
                <Text style={styles.tooltipConfidence}>
                  Confidence: {Math.round(selectedBlock.confidence * 100)}%
                </Text>
              </ScrollView>
            )}
          </View>
        </TouchableOpacity>
      </Modal>
    </>
  );
}

const styles = StyleSheet.create({
  container: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
  },
  textBlock: {
    position: 'absolute',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 2,
    padding: 2,
  },
  blockText: {
    fontSize: 8,
    color: '#1F2937',
    fontWeight: '500',
    textAlign: 'center',
    opacity: 0.8,
  },
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  tooltipContainer: {
    backgroundColor: '#FFFFFF',
    borderRadius: 12,
    padding: 20,
    margin: 20,
    maxWidth: screenWidth * 0.8,
    maxHeight: screenHeight * 0.6,
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  tooltipContent: {
    flex: 1,
  },
  tooltipTitle: {
    fontSize: 18,
    fontWeight: '600',
    color: '#1F2937',
    marginBottom: 12,
  },
  tooltipText: {
    fontSize: 16,
    color: '#374151',
    lineHeight: 24,
    marginBottom: 12,
  },
  tooltipConfidence: {
    fontSize: 14,
    color: '#6B7280',
    fontWeight: '500',
  },
});