import React, { useState, useRef, useCallback, useEffect, forwardRef, useImperativeHandle } from 'react';
import { View, Text, ActivityIndicator, StyleSheet, Platform } from 'react-native';
import { WebView } from 'react-native-webview';
import { PDFViewerSource } from './NativePDF';

export type WebViewPDFViewerProps = {
  source: PDFViewerSource;
  initialZoom?: number;
  rotation?: number;
  style?: any;
  onReady?: () => void;
  onError?: (message: string) => void;
  onMetrics?: (m: { pageCount?: number; currentPage?: number; scale?: number; rotation?: number }) => void;
};

export type WebViewPDFViewerRef = {
  zoomIn: () => void;
  zoomOut: () => void;
  rotate: (deg: number) => void;
  fitWidth: () => void;
};

/**
 * WebView-based PDF viewer using PDF.js
 * Optimized for instant loading and cross-platform consistency
 */
export const WebViewPDFViewer = forwardRef<WebViewPDFViewerRef, WebViewPDFViewerProps>(
  function WebViewPDFViewer(
    { source, initialZoom = 1, rotation = 0, style, onReady, onError, onMetrics },
    ref
  ) {
    const webRef = useRef<WebView>(null);
    const [isLoading, setIsLoading] = useState(true);
    const [error, setError] = useState<string | null>(null);

    // Prepare PDF data for WebView
    const pdfDataUri = React.useMemo(() => {
      if (!source) return null;

      // OPTIMIZATION: Direct file URI support
      if (source.fileUri) {
        console.log('[WebViewPDF] Using direct file URI:', source.fileUri);
        return source.fileUri;
      }

      // Handle base64 data
      if (source.data) {
        if (source.data.startsWith('data:application/pdf;base64,')) {
          return source.data;
        } else {
          // Add data URI prefix for base64
          return `data:application/pdf;base64,${source.data}`;
        }
      }

      return null;
    }, [source]);

    // WebView HTML with PDF.js integration
    const webViewHTML = React.useMemo(() => {
      if (!pdfDataUri) return '';

      return `
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=5.0, user-scalable=yes">
    <title>PDF Viewer</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            background: #f0f0f0;
            font-family: -apple-system, BlinkMacSystemFont, sans-serif;
        }
        #pdfContainer {
            width: 100%;
            height: 100vh;
            display: flex;
            flex-direction: column;
        }
        #controls {
            background: #333;
            color: white;
            padding: 10px;
            display: flex;
            justify-content: center;
            gap: 10px;
            flex-shrink: 0;
        }
        button {
            background: #555;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
        }
        button:hover {
            background: #666;
        }
        #pdfViewer {
            flex: 1;
            width: 100%;
            border: none;
        }
        #status {
            background: #444;
            color: white;
            padding: 5px 10px;
            text-align: center;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div id="pdfContainer">
        <div id="controls">
            <button onclick="zoomOut()">Zoom Out</button>
            <button onclick="fitWidth()">Fit Width</button>
            <button onclick="zoomIn()">Zoom In</button>
            <button onclick="rotate()">Rotate</button>
        </div>
        <div id="status">Loading PDF...</div>
        <iframe id="pdfViewer" src=""></iframe>
    </div>

    <script>
        let currentZoom = ${initialZoom};
        let currentRotation = ${rotation};
        let pdfUrl = '';

        // Initialize PDF viewer
        function initPDF() {
            const viewer = document.getElementById('pdfViewer');
            const status = document.getElementById('status');
            
            try {
                // OPTIMIZATION: Use browser's native PDF viewer for instant loading
                pdfUrl = '${pdfDataUri}';
                
                if (pdfUrl.startsWith('file://')) {
                    // For file URIs, use direct loading
                    viewer.src = pdfUrl;
                } else {
                    // For data URIs, use direct embedding
                    viewer.src = pdfUrl;
                }
                
                status.textContent = 'PDF loaded successfully';
                
                // Notify React Native
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'ready',
                        pageCount: 1, // Browser PDF viewer doesn't expose page count easily
                        currentPage: 1
                    }));
                }
                
            } catch (error) {
                console.error('PDF loading error:', error);
                status.textContent = 'Error loading PDF: ' + error.message;
                
                if (window.ReactNativeWebView) {
                    window.ReactNativeWebView.postMessage(JSON.stringify({
                        type: 'error',
                        message: error.message
                    }));
                }
            }
        }

        // Control functions
        function zoomIn() {
            currentZoom = Math.min(currentZoom * 1.2, 5);
            updateZoom();
        }

        function zoomOut() {
            currentZoom = Math.max(currentZoom / 1.2, 0.25);
            updateZoom();
        }

        function fitWidth() {
            currentZoom = 1;
            updateZoom();
        }

        function rotate() {
            currentRotation = (currentRotation + 90) % 360;
            updateRotation();
        }

        function updateZoom() {
            const viewer = document.getElementById('pdfViewer');
            viewer.style.transform = \`scale(\${currentZoom}) rotate(\${currentRotation}deg)\`;
            
            if (window.ReactNativeWebView) {
                window.ReactNativeWebView.postMessage(JSON.stringify({
                    type: 'metrics',
                    scale: currentZoom,
                    rotation: currentRotation
                }));
            }
        }

        function updateRotation() {
            updateZoom(); // Reuse zoom update logic
        }

        // Initialize when page loads
        document.addEventListener('DOMContentLoaded', initPDF);
        
        // Fallback initialization
        setTimeout(initPDF, 100);
    </script>
</body>
</html>`;
    }, [pdfDataUri, initialZoom, rotation]);

    // Handle WebView messages
    const handleMessage = useCallback((event: any) => {
      try {
        const message = JSON.parse(event.nativeEvent.data);
        
        switch (message.type) {
          case 'ready':
            console.log('[WebViewPDF] PDF loaded successfully');
            setIsLoading(false);
            setError(null);
            onReady?.();
            onMetrics?.({
              pageCount: message.pageCount,
              currentPage: message.currentPage,
              scale: initialZoom,
              rotation: rotation
            });
            break;
            
          case 'error':
            console.error('[WebViewPDF] PDF loading error:', message.message);
            setIsLoading(false);
            setError(message.message);
            onError?.(message.message);
            break;
            
          case 'metrics':
            onMetrics?.({
              scale: message.scale,
              rotation: message.rotation
            });
            break;
        }
      } catch (err) {
        console.error('[WebViewPDF] Message parsing error:', err);
      }
    }, [onReady, onError, onMetrics, initialZoom, rotation]);

    // Expose control methods
    useImperativeHandle(ref, () => ({
      zoomIn: () => webRef.current?.postMessage('zoomIn()'),
      zoomOut: () => webRef.current?.postMessage('zoomOut()'),
      fitWidth: () => webRef.current?.postMessage('fitWidth()'),
      rotate: (deg: number) => webRef.current?.postMessage(`currentRotation = ${deg}; updateRotation();`),
    }), []);

    if (!source || !pdfDataUri) {
      return (
        <View style={[styles.container, style]}>
          <Text style={styles.errorText}>No PDF source provided</Text>
        </View>
      );
    }

    if (error) {
      return (
        <View style={[styles.container, style]}>
          <Text style={styles.errorText}>Error loading PDF: {error}</Text>
        </View>
      );
    }

    return (
      <View style={[styles.container, style]}>
        {isLoading && (
          <View style={styles.loadingOverlay}>
            <ActivityIndicator size="large" color="#007AFF" />
            <Text style={styles.loadingText}>Loading PDF...</Text>
          </View>
        )}
        <WebView
          ref={webRef}
          source={{ html: webViewHTML }}
          onMessage={handleMessage}
          javaScriptEnabled={true}
          domStorageEnabled={true}
          allowFileAccess={true}
          allowUniversalAccessFromFileURLs={true}
          mixedContentMode="compatibility"
          style={styles.webView}
        />
      </View>
    );
  }
);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#f0f0f0',
  },
  webView: {
    flex: 1,
  },
  loadingOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    justifyContent: 'center',
    alignItems: 'center',
    zIndex: 1000,
  },
  loadingText: {
    marginTop: 10,
    fontSize: 16,
    color: '#333',
  },
  errorText: {
    fontSize: 16,
    color: '#ff0000',
    textAlign: 'center',
    margin: 20,
  },
});

export default WebViewPDFViewer;
