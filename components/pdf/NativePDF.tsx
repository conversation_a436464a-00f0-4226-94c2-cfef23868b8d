import React, { useEffect, useImperativeHandle, forwardRef, useState, useRef, useMemo, useCallback } from "react";
import { Platform, StyleProp, ViewStyle, View, Text, ActivityIndicator, Alert } from "react-native";
import * as FileSystem from "expo-file-system";
import Pdf from "react-native-pdf";
import TextOverlay from "./TextOverlay";
import { OCRPage } from "@/utils/ocr";
import { convertBase64ToBlobUri } from "@/utils/files";

// Global cache for PDF files to prevent recreating the same file multiple times
const PDF_FILE_CACHE = new Map<string, string>();

// Helper function to generate a stable hash for PDF data
const generatePdfHash = (data: string): string => {
  // Simple hash function for PDF data - in production, consider using a proper hash library
  let hash = 0;
  for (let i = 0; i < Math.min(data.length, 1000); i++) { // Only hash first 1000 chars for performance
    const char = data.charCodeAt(i);
    hash = ((hash << 5) - hash) + char;
    hash = hash & hash; // Convert to 32-bit integer
  }
  return `pdf_${Math.abs(hash)}_${data.length}`;
};

// Cleanup function for PDF cache (can be called when memory is low)
export const clearPdfCache = async (): Promise<void> => {
  console.log('[NativePDF] Clearing PDF cache...');
  const filesToDelete = Array.from(PDF_FILE_CACHE.values());
  PDF_FILE_CACHE.clear();

  // Delete cached files
  await Promise.allSettled(
    filesToDelete.map(filePath =>
      FileSystem.deleteAsync(filePath, { idempotent: true })
        .catch(err => console.warn('[NativePDF] Failed to delete cached file:', filePath, err))
    )
  );
  console.log('[NativePDF] PDF cache cleared, deleted', filesToDelete.length, 'files');
};

export type PDFViewerSource = {
  kind: "pdf" | "image";
  data: string; // pdf: base64 (no prefix), file URI, or data URL. image: full data URL or file URI
  name?: string;
  // New optimization: direct file URI support
  fileUri?: string; // Direct file URI from DocumentPicker - bypasses base64 conversion
};

export type PDFViewerProps = {
  source: PDFViewerSource;
  initialZoom?: number;
  rotation?: number; // degrees
  style?: StyleProp<ViewStyle>;
  onReady?: () => void;
  onError?: (message: string) => void;
  onMetrics?: (m: { pageCount?: number; currentPage?: number; scale?: number; rotation?: number }) => void;
  // Text overlay props
  textOverlayData?: OCRPage[];
  showTextOverlay?: boolean;
  onTextOverlayToggle?: () => void;
};

export type PDFViewerRef = {
  zoomIn: () => void;
  zoomOut: () => void;
  rotate: (deg: number) => void;
  fitWidth: () => void;
};

interface PageMetrics {
  width: number;
  height: number;
  offsetX: number;
  offsetY: number;
  naturalWidth?: number;
  naturalHeight?: number;
  scale: number;
  rotation: number;
}

export const NativePDF = forwardRef<PDFViewerRef, PDFViewerProps>(function NativePDF(
  {
    source,
    initialZoom = 1,
    rotation = 0,
    style,
    onReady,
    onError,
    onMetrics,
    textOverlayData,
    showTextOverlay = false,
    onTextOverlayToggle
  },
  ref,
) {
  const pdfRef = useRef<Pdf>(null);
  const [pdfSource, setPdfSource] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Add stable reference to prevent recreation during layout changes
  const stablePdfSourceRef = useRef<any>(null);
  
  // State management for PDF viewer
  const [currentPage, setCurrentPage] = useState(0); // 0-based indexing
  const [totalPages, setTotalPages] = useState(0);
  const [currentZoom, setCurrentZoom] = useState(initialZoom);
  const [currentRotation, setCurrentRotation] = useState(rotation);
  const [pageMetrics, setPageMetrics] = useState<Map<number, PageMetrics>>(new Map());
  const [containerLayout, setContainerLayout] = useState<{ x: number; y: number; width: number; height: number } | null>(null);

  // Crash prevention: Add throttling refs for rapid events
  const pageChangeThrottleRef = useRef<NodeJS.Timeout | null>(null);
  const scaleChangeThrottleRef = useRef<NodeJS.Timeout | null>(null);
  const memoryPressureRef = useRef(false);

  // Memoize PDF data hash to prevent unnecessary recalculations
  const pdfDataHash = useMemo(() => {
    if (source.kind !== 'pdf') return null;
    return generatePdfHash(source.data);
  }, [source.kind, source.data]);

  // Memoized PDF source preparation with caching
  const preparePdfSource = useCallback(async (pdfData: string, hash: string): Promise<string> => {
    // Check if we already have this PDF cached
    if (PDF_FILE_CACHE.has(hash)) {
      const cachedPath = PDF_FILE_CACHE.get(hash)!;
      console.log('[NativePDF] Using cached PDF file:', cachedPath);

      // Verify the cached file still exists
      try {
        const fileInfo = await FileSystem.getInfoAsync(cachedPath);
        if (fileInfo.exists) {
          return cachedPath;
        } else {
          console.warn('[NativePDF] Cached file no longer exists, removing from cache');
          PDF_FILE_CACHE.delete(hash);
        }
      } catch (err) {
        console.warn('[NativePDF] Error checking cached file, removing from cache:', err);
        PDF_FILE_CACHE.delete(hash);
      }
    }

    // Create new file and cache it
    console.log('[NativePDF] Creating new PDF file for hash:', hash);
    const tempPath = await convertBase64ToBlobUri(pdfData);
    PDF_FILE_CACHE.set(hash, tempPath);
    return tempPath;
  }, []);

  // Optimized PDF source preparation - supports direct file URIs
  useEffect(() => {
    let mounted = true;
    let loadTimeout: NodeJS.Timeout | undefined;

    async function initializePdfSource() {
      try {
        if (source.kind !== 'pdf') {
          return;
        }

        // OPTIMIZATION 1: Direct file URI usage (bypasses base64 conversion)
        if (source.fileUri) {
          console.log('[NativePDF] Using direct file URI (optimized path):', source.fileUri);

          if (mounted) {
            const optimizedSource = {
              uri: source.fileUri,
              cache: true,
              method: 'GET',
              headers: {},
            };

            // Use stable reference to prevent recreation during layout changes
            if (!stablePdfSourceRef.current || stablePdfSourceRef.current.uri !== source.fileUri) {
              stablePdfSourceRef.current = optimizedSource;
              setPdfSource(optimizedSource);
              console.log('[NativePDF] Direct file URI source set:', source.fileUri);

              // CRITICAL FIX: Set loading to false immediately for direct file URIs
              // The react-native-pdf component will handle the actual loading
              setLoading(false);
              console.log('[NativePDF] Loading state set to false for direct file URI');

              // Set a short timeout to trigger onReady callback
              setTimeout(() => {
                if (mounted) {
                  console.log('[NativePDF] Triggering onReady for direct file URI');
                  onReady?.();
                }
              }, 100);

            } else {
              console.log('[NativePDF] Reusing stable direct file URI reference:', source.fileUri);
              setLoading(false);
            }

            // No long timeout needed for direct file access
            return;
          }
        }

        // FALLBACK: Traditional base64 conversion path
        if (!pdfDataHash) {
          return;
        }

        console.log('[NativePDF] Using base64 conversion (legacy path)...');
        const tempPath = await preparePdfSource(source.data, pdfDataHash);

        if (mounted) {
          // Optimize PDF source for native component
          const optimizedSource = {
            uri: tempPath,
            cache: true,
            // Add native optimization flags
            method: 'GET',
            headers: {},
            // Ensure proper file URI format for Android
            ...(Platform.OS === 'android' && {
              uri: tempPath.startsWith('file://') ? tempPath : `file://${tempPath}`
            })
          };

          // Use stable reference to prevent recreation during layout changes
          if (!stablePdfSourceRef.current || stablePdfSourceRef.current.uri !== tempPath) {
            stablePdfSourceRef.current = optimizedSource;
            setPdfSource(optimizedSource);
            console.log('[NativePDF] PDF source prepared with optimizations:', tempPath);
          } else {
            console.log('[NativePDF] Reusing stable PDF source reference:', tempPath);
            // Still set loading to false if we're reusing the source
            setLoading(false);
          }

          // Reduced timeout with progressive fallback
          loadTimeout = setTimeout(() => {
            if (mounted) {
              console.warn('[NativePDF] PDF load timeout - attempting recovery');
              // Try to trigger load completion manually
              handleLoadComplete(1, tempPath, { width: 800, height: 600 });
            }
          }, 3000); // Reduced to 3 seconds for faster recovery
        }
      } catch (err) {
        console.error('[NativePDF] Failed to prepare PDF source:', err);
        if (mounted) {
          const errorMsg = `Failed to prepare PDF: ${err instanceof Error ? err.message : 'Unknown error'}`;
          setError(errorMsg);
          setLoading(false);
          onError?.(errorMsg);
        }
      }
    }

    if (source.kind === 'pdf' && pdfDataHash) {
      initializePdfSource();
    }

    return () => {
      mounted = false;
      if (loadTimeout) {
        clearTimeout(loadTimeout);
      }
      // Cleanup throttle timers to prevent memory leaks
      if (pageChangeThrottleRef.current) {
        clearTimeout(pageChangeThrottleRef.current);
        pageChangeThrottleRef.current = null;
      }
      if (scaleChangeThrottleRef.current) {
        clearTimeout(scaleChangeThrottleRef.current);
        scaleChangeThrottleRef.current = null;
      }
      // Note: We don't cleanup cached files here as they should be reused
      // Cleanup will happen when the cache is cleared or app restarts
    };
  }, [source.kind, pdfDataHash, preparePdfSource]);

  // CRASH PREVENTION: Memory pressure monitoring
  useEffect(() => {
    const checkMemoryPressure = () => {
      // Simple heuristic: if pageMetrics Map is very large, clear old metrics
      if (pageMetrics.size > 50) {
        console.warn('[NativePDF] High memory usage detected, clearing old metrics');
        memoryPressureRef.current = true;

        // Keep only current page and adjacent pages to reduce memory pressure
        const newMetrics = new Map();
        const keepRange = 3; // Keep 3 pages around current page
        const startPage = Math.max(0, currentPage - keepRange);
        const endPage = Math.min(totalPages - 1, currentPage + keepRange);

        for (let i = startPage; i <= endPage; i++) {
          const metrics = pageMetrics.get(i);
          if (metrics) {
            newMetrics.set(i, metrics);
          }
        }

        console.log(`[NativePDF] Reduced pageMetrics from ${pageMetrics.size} to ${newMetrics.size} pages`);
        setPageMetrics(newMetrics);
        memoryPressureRef.current = false;
      }
    };

    // Check memory pressure every 5 seconds during active use
    const interval = setInterval(checkMemoryPressure, 5000);
    return () => clearInterval(interval);
  }, [pageMetrics, currentPage, totalPages]);

  // Memoized imperative handle for ref methods
  useImperativeHandle(ref, () => ({
    zoomIn: () => {
      const newZoom = Math.min(currentZoom + 0.25, 5);
      setCurrentZoom(newZoom);
      // Note: react-native-pdf doesn't have direct zoom methods,
      // so we'll use the scale prop and trigger re-render
    },
    zoomOut: () => {
      const newZoom = Math.max(currentZoom - 0.25, 0.25);
      setCurrentZoom(newZoom);
    },
    fitWidth: () => {
      if (containerLayout && pageMetrics.size > 0) {
        // Get natural width from first page metrics
        const firstPageMetrics = pageMetrics.get(0);
        if (firstPageMetrics?.naturalWidth) {
          const newScale = containerLayout.width / firstPageMetrics.naturalWidth;
          setCurrentZoom(newScale);
        }
      }
    },
    rotate: (deg: number) => {
      const newRotation = (currentRotation + deg) % 360;
      setCurrentRotation(newRotation);
    },
  }), [currentZoom, currentRotation, containerLayout, pageMetrics]);

  // Memoized callback functions to prevent unnecessary re-renders
  const handleLoadComplete = useCallback((numberOfPages: number, filePath: string, dims?: { width: number; height: number }) => {
    const width = dims?.width ?? containerLayout?.width ?? 0;
    const height = dims?.height ?? containerLayout?.height ?? 0;
    console.log('[NativePDF] PDF loaded successfully:', {
      numberOfPages,
      filePath,
      width,
      height,
      currentLoadingState: loading,
      pdfSourceUri: pdfSource?.uri,
      isDirectFileUri: pdfSource?.uri === source.fileUri
    });
    setTotalPages(numberOfPages);
    setLoading(false);
    console.log('[NativePDF] Loading state set to false via handleLoadComplete');
    setError(null); // Clear any previous errors

    // Generate initial page metrics (0-based indexing)
    const metrics = new Map<number, PageMetrics>();
    const spacing = 10; // From Pdf component props
    for (let i = 0; i < numberOfPages; i++) {
      // Handle width/height swapping for 90°/270° rotations
      const isSwapped = currentRotation % 180 !== 0;
      const displayWidth = isSwapped ? height : width;
      const displayHeight = isSwapped ? width : height;

      // Compute offsetY per page using page index and spacing + page height
      const offsetY = i * (displayHeight + spacing);

      metrics.set(i, {
        width: displayWidth,
        height: displayHeight,
        offsetX: 0,
        offsetY,
        naturalWidth: width,
        naturalHeight: height,
        scale: currentZoom,
        rotation: currentRotation,
      });
    }
    setPageMetrics(metrics);

    onReady?.();
    onMetrics?.({
      pageCount: numberOfPages,
      currentPage: currentPage + 1, // Send 1-based to consumer
      scale: currentZoom,
      rotation: currentRotation
    });
  }, [containerLayout, currentRotation, currentZoom, currentPage, onReady, onMetrics]);

  const handlePageChanged = useCallback((page: number, numberOfPages: number) => {
    // Add safety checks to prevent crashes during rapid navigation
    if (page < 1 || page > numberOfPages) {
      console.warn('[NativePDF] Invalid page number:', page, 'of', numberOfPages);
      return;
    }

    // CRASH PREVENTION: Clear previous throttle to prevent memory leaks
    if (pageChangeThrottleRef.current) {
      clearTimeout(pageChangeThrottleRef.current);
      pageChangeThrottleRef.current = null;
    }

    // CRASH PREVENTION: Throttle all page changes during continuous scrolling
    pageChangeThrottleRef.current = setTimeout(() => {
      console.log('[NativePDF] Page changed (throttled):', { page, numberOfPages });
      const zeroBasedPage = page - 1;

      // Batch state updates to prevent excessive re-renders
      setCurrentPage(zeroBasedPage);

      // Defer metrics callback to next frame to prevent blocking
      requestAnimationFrame(() => {
        onMetrics?.({
          pageCount: numberOfPages,
          currentPage: page, // Send 1-based to consumer
          scale: currentZoom,
          rotation: currentRotation
        });
      });
    }, 150); // Increased throttle time for better crash prevention
  }, [currentZoom, currentRotation, onMetrics]);

  const handleScaleChanged = useCallback((scale: number) => {
    console.log('[NativePDF] Scale changed:', scale);
    setCurrentZoom(scale);

    // CRASH PREVENTION: Clear previous throttle
    if (scaleChangeThrottleRef.current) {
      clearTimeout(scaleChangeThrottleRef.current);
      scaleChangeThrottleRef.current = null;
    }

    // CRASH PREVENTION: Throttle expensive metrics recalculation
    scaleChangeThrottleRef.current = setTimeout(() => {
      // Defer expensive calculation to prevent blocking main thread
      requestAnimationFrame(() => {
        if (pageMetrics.size === 0) return;

        // MEMORY OPTIMIZATION: Only update visible pages during scrolling
        const visiblePageStart = Math.max(0, currentPage - 2);
        const visiblePageEnd = Math.min(totalPages - 1, currentPage + 2);
        const updatedMetrics = new Map();
        const spacing = 10;

        // Only calculate metrics for visible pages to reduce memory pressure
        for (let pageNum = visiblePageStart; pageNum <= visiblePageEnd; pageNum++) {
          const metrics = pageMetrics.get(pageNum);
          if (metrics) {
            const scaledWidth = (metrics.naturalWidth || metrics.width) * scale;
            const scaledHeight = (metrics.naturalHeight || metrics.height) * scale;
            const offsetY = pageNum * (scaledHeight + spacing);

            updatedMetrics.set(pageNum, {
              ...metrics,
              scale,
              width: scaledWidth,
              height: scaledHeight,
              offsetY,
            });
          }
        }

        setPageMetrics(updatedMetrics);

        // Defer callback to prevent cascading updates
        requestAnimationFrame(() => {
          onMetrics?.({
            pageCount: totalPages,
            currentPage: currentPage + 1, // Send 1-based
            scale,
            rotation: currentRotation
          });
        });
      });
    }, 100); // Throttle scale changes
  }, [pageMetrics, totalPages, currentPage, currentRotation, onMetrics]);

  const handleError = useCallback((error: any) => {
    console.error('[NativePDF] PDF load error:', error);
    const errorMsg = `Failed to load PDF: ${error?.message || error?.toString() || 'Unknown error'}`;
    setError(errorMsg);
    setLoading(false);
    onError?.(errorMsg);
  }, [onError]);

  // Memoized container layout handler
  const handleContainerLayout = useCallback((event: any) => {
    const { x, y, width, height } = event.nativeEvent.layout;
    setContainerLayout({ x, y, width, height });
  }, []);

  // Fallback for unsupported platforms or errors
  const renderFallback = () => {
    if (error) {
      return (
        <View style={[styles.container, styles.centered, style]}>
          <Text style={styles.errorText}>PDF Load Error</Text>
          <Text style={styles.errorMessage}>{error}</Text>
        </View>
      );
    }

    if (loading) {
      return (
        <View style={[styles.container, styles.centered, style]}>
          <ActivityIndicator size="large" color="#007AFF" />
          <Text style={styles.loadingText}>Loading PDF...</Text>
        </View>
      );
    }

    return null;
  };

  // Only render PDF on iOS/Android
  if (Platform.OS !== 'ios' && Platform.OS !== 'android') {
    return (
      <View style={[styles.container, styles.centered, style]}>
        <Text style={styles.errorText}>Platform Not Supported</Text>
        <Text style={styles.errorMessage}>Native PDF viewer is only available on iOS and Android</Text>
      </View>
    );
  }

  // Handle image sources (maintain compatibility)
  if (source.kind === 'image') {
    return (
      <View style={[styles.container, style]}>
        <Text style={styles.errorText}>Image viewing not supported in native PDF viewer</Text>
        <Text style={styles.errorMessage}>Please use the web-based viewer for image sources</Text>
      </View>
    );
  }

  // Show fallback while preparing or on error
  if (!pdfSource || error || loading) {
    return renderFallback();
  }

  return (
    <View
      style={[styles.container, style]}
      onLayout={handleContainerLayout}
    >
      <View style={[styles.pdfWrap, { transform: [{ rotate: `${currentRotation}deg` }] }]}>
        <Pdf
          ref={pdfRef}
          source={pdfSource}
          scale={currentZoom}
          minScale={0.25}
          maxScale={3} // Reduced max scale to prevent memory issues
          horizontal={false}
          page={currentPage + 1} // react-native-pdf expects 1-based page
          onLoadComplete={handleLoadComplete}
          onPageChanged={handlePageChanged}
          onScaleChanged={handleScaleChanged}
          onError={handleError}
          style={styles.pdf}
          enablePaging={true}
          enableRTL={false}
          enableAnnotationRendering={false} // Disable to reduce memory usage
          password=""
          spacing={10}
          fitPolicy={0} // 0: fit width, 1: fit height, 2: fit both
          // Performance optimizations
          enableDoubleTapZoom={false} // Prevent rapid zoom changes that can cause crashes
          // Crash prevention - throttle progress updates
          onLoadProgress={(percent) => {
            if (percent % 20 === 0) { // Only log every 20%
              console.log('[NativePDF] Load progress:', percent + '%');
            }
          }}
        />
      </View>
      
      <TextOverlay
        ocrPages={textOverlayData || []}
        currentPage={currentPage} // Already 0-based
        zoom={currentZoom}
        rotation={currentRotation}
        visible={showTextOverlay}
        onToggleVisibility={onTextOverlayToggle}
        style={styles.textOverlay}
        pageMetrics={pageMetrics}
        containerLayout={containerLayout}
      />
    </View>
  );
});

const styles = {
  container: {
    flex: 1,
    position: 'relative' as const,
  },
  centered: {
    justifyContent: 'center' as const,
    alignItems: 'center' as const,
  },
  pdfWrap: {
    flex: 1,
  },
  pdf: {
    flex: 1,
  },
  textOverlay: {
    position: 'absolute' as const,
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 10,
  },
  errorText: {
    fontSize: 18,
    fontWeight: 'bold' as const,
    color: '#FF3B30',
    marginBottom: 8,
    textAlign: 'center' as const,
  },
  errorMessage: {
    fontSize: 14,
    color: '#666',
    textAlign: 'center' as const,
    paddingHorizontal: 20,
  },
  loadingText: {
    fontSize: 16,
    color: '#666',
    marginTop: 12,
    textAlign: 'center' as const,
  },
};

export default NativePDF;
