#!/bin/bash

# PDF Scrolling Crash Log Capture Script
# Usage: ./scripts/capture-crash-logs.sh

echo "🔍 PDF Scrolling Crash Investigation - Log Capture Script"
echo "=========================================================="

# Check if adb is available
if ! command -v adb &> /dev/null; then
    echo "❌ Error: adb command not found. Please install Android SDK platform-tools."
    exit 1
fi

# Check if device is connected
if ! adb devices | grep -q "device$"; then
    echo "❌ Error: No Android device connected. Please connect your device and enable USB debugging."
    echo "Connected devices:"
    adb devices
    exit 1
fi

echo "✅ Android device detected"

# Create logs directory if it doesn't exist
mkdir -p logs/crash-investigation

# Get timestamp for log files
TIMESTAMP=$(date +"%Y%m%d_%H%M%S")
LOG_FILE="logs/crash-investigation/pdf_crash_${TIMESTAMP}.txt"
FILTERED_LOG_FILE="logs/crash-investigation/pdf_crash_filtered_${TIMESTAMP}.txt"

echo "📱 Device Information:"
echo "====================="
adb shell getprop ro.product.model
adb shell getprop ro.build.version.release
adb shell getprop ro.build.version.sdk

echo ""
echo "🧹 Clearing existing logs..."
adb logcat -c

echo "📝 Starting log capture..."
echo "Log file: $LOG_FILE"
echo "Filtered log file: $FILTERED_LOG_FILE"
echo ""
echo "🎯 INSTRUCTIONS:"
echo "1. Open your PDF app"
echo "2. Load a PDF document"
echo "3. Start continuous vertical scrolling (up and down rapidly)"
echo "4. Continue scrolling for 30-60 seconds until crash occurs"
echo "5. Press Ctrl+C when the crash happens to stop logging"
echo ""
echo "⚠️  IMPORTANT: Keep scrolling continuously to reproduce the crash!"
echo ""
echo "Starting log capture in 5 seconds..."
sleep 5

# Start full log capture in background
echo "🚀 Log capture started. Scroll continuously in your PDF app now!"
adb logcat > "$LOG_FILE" &
LOGCAT_PID=$!

# Start filtered log capture for critical errors
adb logcat -s ReactNativeJS:V ReactNative:V AndroidRuntime:E System.err:V ActivityManager:W > "$FILTERED_LOG_FILE" &
FILTERED_PID=$!

# Wait for user to stop (Ctrl+C)
trap 'echo ""; echo "🛑 Stopping log capture..."; kill $LOGCAT_PID $FILTERED_PID 2>/dev/null; exit 0' INT

echo "Press Ctrl+C when crash occurs to stop logging..."
wait $LOGCAT_PID

# This should not be reached due to trap, but just in case
kill $FILTERED_PID 2>/dev/null

echo ""
echo "✅ Log capture completed!"
echo "📁 Full logs saved to: $LOG_FILE"
echo "📁 Filtered logs saved to: $FILTERED_LOG_FILE"
echo ""
echo "🔍 Quick Analysis:"
echo "=================="

# Check for common crash patterns
echo "Checking for OutOfMemoryError..."
if grep -q "OutOfMemoryError" "$LOG_FILE"; then
    echo "❌ FOUND: OutOfMemoryError detected"
    grep -n "OutOfMemoryError" "$LOG_FILE" | head -5
else
    echo "✅ No OutOfMemoryError found"
fi

echo ""
echo "Checking for SIGSEGV (segmentation fault)..."
if grep -q "SIGSEGV" "$LOG_FILE"; then
    echo "❌ FOUND: SIGSEGV detected"
    grep -n "SIGSEGV" "$LOG_FILE" | head -5
else
    echo "✅ No SIGSEGV found"
fi

echo ""
echo "Checking for React Native errors..."
if grep -q "ReactNativeJS.*Exception" "$LOG_FILE"; then
    echo "❌ FOUND: React Native exceptions detected"
    grep -n "ReactNativeJS.*Exception" "$LOG_FILE" | head -5
else
    echo "✅ No React Native exceptions found"
fi

echo ""
echo "Checking for PDF-specific errors..."
if grep -q -i "pdf.*error\|pdf.*crash\|pdf.*fail" "$LOG_FILE"; then
    echo "❌ FOUND: PDF-related errors detected"
    grep -n -i "pdf.*error\|pdf.*crash\|pdf.*fail" "$LOG_FILE" | head -5
else
    echo "✅ No PDF-specific errors found"
fi

echo ""
echo "📊 Log Statistics:"
echo "=================="
echo "Total log lines: $(wc -l < "$LOG_FILE")"
echo "Error lines: $(grep -c "E/" "$LOG_FILE")"
echo "Warning lines: $(grep -c "W/" "$LOG_FILE")"
echo "Fatal lines: $(grep -c "F/" "$LOG_FILE")"

echo ""
echo "🎯 Next Steps:"
echo "=============="
echo "1. Review the log files above"
echo "2. Look for the specific error patterns mentioned in the analysis"
echo "3. Share the relevant error messages for detailed investigation"
echo "4. Apply the crash prevention fixes in NativePDF.tsx"
echo ""
echo "📋 Key files to examine:"
echo "- $LOG_FILE (full logs)"
echo "- $FILTERED_LOG_FILE (filtered critical errors)"
echo "- docs/crash-investigation/pdf-scrolling-crash-analysis.md (analysis guide)"
