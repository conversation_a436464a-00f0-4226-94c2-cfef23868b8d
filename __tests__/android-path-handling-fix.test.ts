/**
 * Android Path Handling Fix Test
 * 
 * This test verifies the Android-specific path handling and file verification
 * logic that handles /storage/emulated/0/ paths and converts them properly
 * for Expo FileSystem compatibility.
 */

import * as FileSystem from 'expo-file-system';
import { Platform } from 'react-native';

// Mock Platform
jest.mock('react-native', () => ({
  Platform: {
    OS: 'android',
  },
}));

// Mock FileSystem
jest.mock('expo-file-system', () => ({
  documentDirectory: 'file:///documents/',
  cacheDirectory: 'file:///cache/',
  getInfoAsync: jest.fn(),
  writeAsStringAsync: jest.fn(),
  deleteAsync: jest.fn(),
  copyAsync: jest.fn(),
  EncodingType: {
    Base64: 'base64',
  },
}));

const mockedFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;

// Import the functions after mocks are set up
import { normalizeAndroidPath, verifyFileExistsAndroid, generatePdfFromImages } from '../utils/files';

// Get the mocked createPdf function from the global mock
const { createPdf: mockCreatePdf } = require('react-native-pdf-from-image');

describe('Android Path Handling Fix', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset console methods
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    
    // Default file system mocks
    mockedFileSystem.writeAsStringAsync.mockResolvedValue();
    mockedFileSystem.deleteAsync.mockResolvedValue();
    mockedFileSystem.copyAsync.mockResolvedValue();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  describe('normalizeAndroidPath', () => {
    test('should convert Android external storage paths to file URIs', () => {
      const androidPath = '/storage/emulated/0/Android/data/app.rork.aipdfassistant/cache/test.pdf';
      const result = normalizeAndroidPath(androidPath);
      expect(result).toBe('file:///storage/emulated/0/Android/data/app.rork.aipdfassistant/cache/test.pdf');
    });

    test('should convert Android app-specific paths to file URIs', () => {
      const androidPath = '/data/user/0/app.rork.aipdfassistant/files/test.pdf';
      const result = normalizeAndroidPath(androidPath);
      expect(result).toBe('file:///data/user/0/app.rork.aipdfassistant/files/test.pdf');
    });

    test('should return file URIs unchanged', () => {
      const fileUri = 'file:///documents/test.pdf';
      const result = normalizeAndroidPath(fileUri);
      expect(result).toBe(fileUri);
    });

    test('should convert other absolute paths to file URIs', () => {
      const absolutePath = '/some/other/path/test.pdf';
      const result = normalizeAndroidPath(absolutePath);
      expect(result).toBe('file:///some/other/path/test.pdf');
    });

    test('should return non-path strings unchanged', () => {
      const nonPath = 'not-a-path';
      const result = normalizeAndroidPath(nonPath);
      expect(result).toBe(nonPath);
    });

    test('should handle empty or null input', () => {
      expect(normalizeAndroidPath('')).toBe('');
      expect(normalizeAndroidPath(null as any)).toBe(null);
      expect(normalizeAndroidPath(undefined as any)).toBe(undefined);
    });
  });

  describe('verifyFileExistsAndroid', () => {
    test('should find file at original path', async () => {
      const originalPath = '/storage/emulated/0/test.pdf';
      
      mockedFileSystem.getInfoAsync.mockImplementation((path) => {
        if (path === originalPath) {
          return Promise.resolve({
            exists: true,
            isDirectory: false,
            size: 1024000,
            modificationTime: Date.now(),
            uri: originalPath,
          });
        }
        return Promise.resolve({
          exists: false,
          isDirectory: false,
          size: 0,
          modificationTime: 0,
          uri: '',
        });
      });

      const result = await verifyFileExistsAndroid(originalPath);
      
      expect(result.exists).toBe(true);
      expect(result.actualPath).toBe(originalPath);
      expect(result.fileInfo?.size).toBe(1024000);
    });

    test('should find file at normalized path when original fails', async () => {
      const originalPath = '/storage/emulated/0/test.pdf';
      const normalizedPath = 'file:///storage/emulated/0/test.pdf';
      
      mockedFileSystem.getInfoAsync.mockImplementation((path) => {
        if (path === normalizedPath) {
          return Promise.resolve({
            exists: true,
            isDirectory: false,
            size: 2048000,
            modificationTime: Date.now(),
            uri: normalizedPath,
          });
        }
        return Promise.resolve({
          exists: false,
          isDirectory: false,
          size: 0,
          modificationTime: 0,
          uri: '',
        });
      });

      const result = await verifyFileExistsAndroid(originalPath);
      
      expect(result.exists).toBe(true);
      expect(result.actualPath).toBe(normalizedPath);
      expect(result.fileInfo?.size).toBe(2048000);
    });

    test('should find file at expected path when original and normalized fail', async () => {
      const originalPath = '/storage/emulated/0/test.pdf';
      const expectedPath = 'file:///documents/test.pdf';
      
      mockedFileSystem.getInfoAsync.mockImplementation((path) => {
        if (path === expectedPath) {
          return Promise.resolve({
            exists: true,
            isDirectory: false,
            size: 512000,
            modificationTime: Date.now(),
            uri: expectedPath,
          });
        }
        return Promise.resolve({
          exists: false,
          isDirectory: false,
          size: 0,
          modificationTime: 0,
          uri: '',
        });
      });

      const result = await verifyFileExistsAndroid(originalPath, expectedPath);
      
      expect(result.exists).toBe(true);
      expect(result.actualPath).toBe(expectedPath);
      expect(result.fileInfo?.size).toBe(512000);
    });

    test('should return false when file does not exist at any path', async () => {
      const originalPath = '/storage/emulated/0/test.pdf';
      const expectedPath = 'file:///documents/test.pdf';
      
      mockedFileSystem.getInfoAsync.mockResolvedValue({
        exists: false,
        isDirectory: false,
        size: 0,
        modificationTime: 0,
        uri: '',
      });

      const result = await verifyFileExistsAndroid(originalPath, expectedPath);
      
      expect(result.exists).toBe(false);
      expect(result.actualPath).toBe(originalPath);
      expect(result.fileInfo).toBeUndefined();
    });

    test('should handle FileSystem errors gracefully', async () => {
      const originalPath = '/storage/emulated/0/test.pdf';
      
      mockedFileSystem.getInfoAsync.mockRejectedValue(new Error('Permission denied'));

      const result = await verifyFileExistsAndroid(originalPath);
      
      expect(result.exists).toBe(false);
      expect(result.actualPath).toBe(originalPath);
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('Failed to check'),
        expect.stringContaining('Permission denied')
      );
    });

    test('should log detailed verification attempts', async () => {
      const originalPath = '/storage/emulated/0/test.pdf';
      
      mockedFileSystem.getInfoAsync.mockResolvedValue({
        exists: false,
        isDirectory: false,
        size: 0,
        modificationTime: 0,
        uri: '',
      });

      await verifyFileExistsAndroid(originalPath);
      
      expect(console.log).toHaveBeenCalledWith(
        '[File Verification] Checking path: /storage/emulated/0/test.pdf'
      );
      expect(console.log).toHaveBeenCalledWith(
        '[File Verification] Checking path: file:///storage/emulated/0/test.pdf'
      );
    });
  });

  describe('Integration with PDF Generation', () => {
    test('should handle Android external storage paths in PDF generation', async () => {
      const androidLibraryPath = '/storage/emulated/0/Android/data/app.rork.aipdfassistant/cache/test.pdf';
      const normalizedPath = 'file:///storage/emulated/0/Android/data/app.rork.aipdfassistant/cache/test.pdf';
      
      // Mock library returning Android external storage path
      mockCreatePdf.mockResolvedValue({
        filePath: androidLibraryPath,
        success: true,
      });

      // Mock file exists at normalized path
      mockedFileSystem.getInfoAsync.mockImplementation((path) => {
        if (path === normalizedPath) {
          return Promise.resolve({
            exists: true,
            isDirectory: false,
            size: 3072000,
            modificationTime: Date.now(),
            uri: normalizedPath,
          });
        }
        return Promise.resolve({
          exists: false,
          isDirectory: false,
          size: 0,
          modificationTime: 0,
          uri: '',
        });
      });

      const result = await generatePdfFromImages(['file:///sample/image.jpg']);
      
      expect(result).toBe(normalizedPath);
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('Path mismatch detected')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('File found at:')
      );
    });

    test('should handle Android app-specific paths in PDF generation', async () => {
      const androidLibraryPath = '/data/user/0/app.rork.aipdfassistant/files/test.pdf';
      const normalizedPath = 'file:///data/user/0/app.rork.aipdfassistant/files/test.pdf';
      
      // Mock library returning Android app-specific path
      mockCreatePdf.mockResolvedValue({
        filePath: androidLibraryPath,
        success: true,
      });

      // Mock file exists at original path (some Android versions allow direct access)
      mockedFileSystem.getInfoAsync.mockImplementation((path) => {
        if (path === androidLibraryPath) {
          return Promise.resolve({
            exists: true,
            isDirectory: false,
            size: 1536000,
            modificationTime: Date.now(),
            uri: androidLibraryPath,
          });
        }
        return Promise.resolve({
          exists: false,
          isDirectory: false,
          size: 0,
          modificationTime: 0,
          uri: '',
        });
      });

      const result = await generatePdfFromImages(['file:///sample/image.jpg']);
      
      expect(result).toBe(androidLibraryPath);
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('File found at:')
      );
    });
  });
});
