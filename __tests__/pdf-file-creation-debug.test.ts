/**
 * Comprehensive End-to-End PDF File Creation Debug Tests
 *
 * This test suite investigates the PDF file creation issue where the library
 * appears to succeed but the actual PDF file is not created at the expected path.
 */

// Mock the react-native-pdf-from-image library BEFORE importing anything
const mockCreatePdf = jest.fn();
jest.mock('react-native-pdf-from-image', () => ({
  createPdf: mockCreatePdf,
}), { virtual: true });

// Mock FileSystem operations
jest.mock('expo-file-system', () => ({
  documentDirectory: 'file:///documents/',
  cacheDirectory: 'file:///cache/',
  getInfoAsync: jest.fn(),
  writeAsStringAsync: jest.fn(),
  deleteAsync: jest.fn(),
  copyAsync: jest.fn(),
  EncodingType: {
    Base64: 'base64',
  },
}));

// Mock OCR functions
jest.mock('../utils/ocr', () => ({
  performOcrOnImages: jest.fn(),
  concatenateOcrText: jest.fn(),
}));

import * as FileSystem from 'expo-file-system';
import { generatePdfFromImages, createDocumentWithOcr } from '../utils/files';

const mockedFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;

describe('PDF File Creation Debug Tests', () => {
  const sampleImagePath = '/home/<USER>/ReactExpoProjects/rork-ai-pdf-assistant/assets/sample/img-sample.jpeg';
  const sampleImageUri = `file://${sampleImagePath}`;

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Default successful file system mocks
    mockedFileSystem.getInfoAsync.mockResolvedValue({
      exists: true,
      isDirectory: false,
      size: 1024000, // 1MB
      modificationTime: Date.now(),
      uri: 'file:///documents/test.pdf',
    });
    
    mockedFileSystem.writeAsStringAsync.mockResolvedValue();
    mockedFileSystem.deleteAsync.mockResolvedValue();
    mockedFileSystem.copyAsync.mockResolvedValue();
  });

  describe('PDF Library Integration Issues', () => {
    test('should detect when library returns success but file is not created', async () => {
      // Mock library returning success response
      mockCreatePdf.mockResolvedValue({
        filePath: '/different/path/than/expected.pdf',
        success: true,
      });

      // Mock file system showing the expected file doesn't exist
      mockedFileSystem.getInfoAsync.mockResolvedValue({
        exists: false,
        isDirectory: false,
        size: 0,
        modificationTime: 0,
        uri: '',
      });

      await expect(generatePdfFromImages([sampleImageUri])).rejects.toThrow(
        'Generated PDF file does not exist at path'
      );

      // Verify the library was called with correct parameters
      expect(mockCreatePdf).toHaveBeenCalledWith({
        imagePaths: [sampleImageUri],
        name: expect.stringMatching(/document_.*\.pdf/),
        paperSize: 'A4',
      });
    });

    test('should handle library returning undefined/null response', async () => {
      // Mock library returning undefined (common issue)
      mockCreatePdf.mockResolvedValue(undefined);

      // Mock file system showing file doesn't exist
      mockedFileSystem.getInfoAsync.mockResolvedValue({
        exists: false,
        isDirectory: false,
        size: 0,
        modificationTime: 0,
        uri: '',
      });

      await expect(generatePdfFromImages([sampleImageUri])).rejects.toThrow(
        'Generated PDF file does not exist at path'
      );
    });

    test('should handle library returning different file path than expected', async () => {
      // Mock library creating file at different location
      const actualLibraryPath = '/data/user/0/app.rork.aipdfassistant/files/different_name.pdf';
      mockCreatePdf.mockResolvedValue({
        filePath: actualLibraryPath,
        success: true,
      });

      // Mock our expected path doesn't exist
      mockedFileSystem.getInfoAsync.mockImplementation((path) => {
        if (typeof path === 'string' && path.includes('document_')) {
          // Our expected path doesn't exist
          return Promise.resolve({
            exists: false,
            isDirectory: false,
            size: 0,
            modificationTime: 0,
            uri: '',
          });
        }
        // Other paths exist
        return Promise.resolve({
          exists: true,
          isDirectory: false,
          size: 1024000,
          modificationTime: Date.now(),
          uri: path as string,
        });
      });

      await expect(generatePdfFromImages([sampleImageUri])).rejects.toThrow(
        'Generated PDF file does not exist at path'
      );
    });

    test('should handle library internal errors gracefully', async () => {
      // Mock library throwing internal error
      mockCreatePdf.mockRejectedValue(new Error('Library internal error: Cannot write to path'));

      await expect(generatePdfFromImages([sampleImageUri])).rejects.toThrow(
        'Failed to generate PDF: Library internal error: Cannot write to path'
      );
    });
  });

  describe('File Path Mismatch Investigation', () => {
    test('should verify expected vs actual file paths', async () => {
      const expectedPath = 'file:///documents/Scanned_Document_9-25-2025.pdf';
      const actualLibraryPath = '/data/user/0/app.rork.aipdfassistant/files/Scanned_Document_9-25-2025.pdf';

      // Mock library creating file at system path
      mockCreatePdf.mockResolvedValue({
        filePath: actualLibraryPath,
        success: true,
      });

      // Mock file system calls
      mockedFileSystem.getInfoAsync.mockImplementation((path) => {
        if (path === expectedPath) {
          // Our expected path doesn't exist
          return Promise.resolve({
            exists: false,
            isDirectory: false,
            size: 0,
            modificationTime: 0,
            uri: '',
          });
        }
        if (path === actualLibraryPath) {
          // Library's actual path exists
          return Promise.resolve({
            exists: true,
            isDirectory: false,
            size: 1024000,
            modificationTime: Date.now(),
            uri: actualLibraryPath,
          });
        }
        return Promise.resolve({
          exists: false,
          isDirectory: false,
          size: 0,
          modificationTime: 0,
          uri: '',
        });
      });

      await expect(generatePdfFromImages([sampleImageUri], {
        nameOverride: 'Scanned_Document_9-25-2025.pdf'
      })).rejects.toThrow('Generated PDF file does not exist at path');

      // Verify we checked the expected path
      expect(mockedFileSystem.getInfoAsync).toHaveBeenCalledWith(expectedPath);
    });

    test('should handle directory permission issues', async () => {
      // Mock library succeeding
      mockCreatePdf.mockResolvedValue({
        filePath: '/documents/test.pdf',
        success: true,
      });

      // Mock file system permission error
      mockedFileSystem.getInfoAsync.mockRejectedValue(new Error('Permission denied'));

      await expect(generatePdfFromImages([sampleImageUri])).rejects.toThrow(
        'Generated PDF file verification failed: Permission denied'
      );
    });
  });

  describe('End-to-End Document Creation with Real Sample Image', () => {
    test('should create document with real sample image path', async () => {
      // Mock successful PDF creation
      const expectedPdfPath = 'file:///documents/test_document.pdf';
      mockCreatePdf.mockResolvedValue({
        filePath: expectedPdfPath,
        success: true,
      });

      // Mock file exists after creation
      mockedFileSystem.getInfoAsync.mockResolvedValue({
        exists: true,
        isDirectory: false,
        size: 2048000, // 2MB
        modificationTime: Date.now(),
        uri: expectedPdfPath,
      });

      // Mock OCR functions
      const { performOcrOnImages, concatenateOcrText } = require('../utils/ocr');
      performOcrOnImages.mockResolvedValue([
        { fullText: 'Sample text from image', blocks: [] }
      ]);
      concatenateOcrText.mockReturnValue('Sample text from image');

      const result = await createDocumentWithOcr([sampleImageUri], {
        documentName: 'test_document.pdf',
        skipOcr: false,
      });

      expect(result).toBeDefined();
      expect(result.uri).toBe(expectedPdfPath);
      expect(result.size).toBe(2048000);
      expect(result.originalImages).toEqual([sampleImageUri]);
      expect(result.ocrText).toBe('Sample text from image');

      // Verify PDF generation was called with correct parameters
      expect(mockCreatePdf).toHaveBeenCalledWith({
        imagePaths: [sampleImageUri],
        name: 'test_document.pdf',
        paperSize: 'A4',
      });
    });

    test('should handle OCR failure but still create PDF successfully', async () => {
      // Mock successful PDF creation
      const expectedPdfPath = 'file:///documents/test_document.pdf';
      mockCreatePdf.mockResolvedValue({
        filePath: expectedPdfPath,
        success: true,
      });

      // Mock file exists after creation
      mockedFileSystem.getInfoAsync.mockResolvedValue({
        exists: true,
        isDirectory: false,
        size: 1536000, // 1.5MB
        modificationTime: Date.now(),
        uri: expectedPdfPath,
      });

      // Mock OCR failure
      const { performOcrOnImages } = require('../utils/ocr');
      performOcrOnImages.mockRejectedValue(new Error('OCR processing failed'));

      const result = await createDocumentWithOcr([sampleImageUri], {
        documentName: 'test_document.pdf',
        skipOcr: false, // OCR enabled but will fail
      });

      expect(result).toBeDefined();
      expect(result.uri).toBe(expectedPdfPath);
      expect(result.size).toBe(1536000);
      expect(result.ocrText).toBe(''); // Empty due to OCR failure
      expect(result.ocrProcessed).toBe(false);

      // Verify PDF generation still succeeded
      expect(mockCreatePdf).toHaveBeenCalledWith({
        imagePaths: [sampleImageUri],
        name: 'test_document.pdf',
        paperSize: 'A4',
      });
    });
  });

  describe('Library Response Analysis', () => {
    test('should log and analyze different library response formats', async () => {
      const consoleSpy = jest.spyOn(console, 'log').mockImplementation();

      // Test various response formats the library might return
      const testCases = [
        { filePath: '/path/to/file.pdf' },
        { filePath: '/path/to/file.pdf', success: true },
        { path: '/path/to/file.pdf' }, // Wrong property name
        '/path/to/file.pdf', // String instead of object
        null,
        undefined,
      ];

      for (const [index, response] of testCases.entries()) {
        mockCreatePdf.mockResolvedValue(response);
        
        // Mock file doesn't exist to trigger error
        mockedFileSystem.getInfoAsync.mockResolvedValue({
          exists: false,
          isDirectory: false,
          size: 0,
          modificationTime: 0,
          uri: '',
        });

        try {
          await generatePdfFromImages([sampleImageUri]);
        } catch (error) {
          // Expected to fail
        }

        // Verify the response was logged
        expect(consoleSpy).toHaveBeenCalledWith(
          '[PDF Generation] Library returned:',
          response
        );
      }

      consoleSpy.mockRestore();
    });
  });
});
