import { createDocumentWithOcr } from '../utils/files';
import { performOcrOnImages, concatenateOcrText } from '../utils/ocr';
import * as FileSystem from 'expo-file-system';

// Mock dependencies
jest.mock('expo-file-system');
jest.mock('../utils/ocr');
jest.mock('react-native-pdf-from-image', () => ({
  createPdf: jest.fn(),
}));

const mockFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;
const mockPerformOcrOnImages = performOcrOnImages as jest.MockedFunction<typeof performOcrOnImages>;
const mockConcatenateOcrText = concatenateOcrText as jest.MockedFunction<typeof concatenateOcrText>;

// Import the mocked createPdf
const mockCreatePdf = require('react-native-pdf-from-image').createPdf;

describe('OCR Optional Fix Verification', () => {
  const mockImageUris = ['file://test1.jpg', 'file://test2.jpg'];
  const mockPdfPath = '/path/to/generated.pdf';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Mock successful PDF generation
    mockCreatePdf.mockResolvedValue({ filePath: mockPdfPath });
    
    // Mock file system operations
    mockFileSystem.getInfoAsync.mockResolvedValue({
      exists: true,
      size: 1024000,
      isDirectory: false,
      uri: mockPdfPath,
      modificationTime: Date.now(),
    });
    
    // Mock successful OCR by default
    mockPerformOcrOnImages.mockResolvedValue({
      results: [
        { fullText: 'Page 1 text', blocks: [], width: 800, height: 600 },
        { fullText: 'Page 2 text', blocks: [], width: 800, height: 600 },
      ],
      errors: [],
    });
    
    mockConcatenateOcrText.mockReturnValue('Page 1 text\n\nPage 2 text');
  });

  describe('Default Behavior Changes', () => {
    it('should skip OCR by default (skipOcr: true)', async () => {
      const result = await createDocumentWithOcr(mockImageUris);

      expect(result).toBeDefined();
      expect(result.ocrProcessed).toBe(false);
      expect(result.ocrText).toBe('');
      expect(mockPerformOcrOnImages).not.toHaveBeenCalled();
      expect(mockConcatenateOcrText).not.toHaveBeenCalled();
    });

    it('should process OCR when explicitly enabled (skipOcr: false)', async () => {
      const result = await createDocumentWithOcr(mockImageUris, { skipOcr: false });

      expect(result).toBeDefined();
      expect(result.ocrProcessed).toBe(true);
      expect(result.ocrText).toBe('Page 1 text\n\nPage 2 text');
      expect(mockPerformOcrOnImages).toHaveBeenCalledWith(mockImageUris, expect.any(Object));
      expect(mockConcatenateOcrText).toHaveBeenCalled();
    });
  });

  describe('OCR Error Isolation', () => {
    it('should succeed with PDF generation even when OCR fails completely', async () => {
      mockPerformOcrOnImages.mockRejectedValue(new Error('OCR library not available'));

      const result = await createDocumentWithOcr(mockImageUris, { skipOcr: false });

      expect(result).toBeDefined();
      expect(result.ocrProcessed).toBe(false);
      expect(result.ocrText).toBe('');
      expect(result.uri).toContain('document_'); // PDF path is generated dynamically
      expect(mockCreatePdf).toHaveBeenCalled();
    });

    it('should handle OCR result processing errors gracefully', async () => {
      mockPerformOcrOnImages.mockResolvedValue({
        results: [
          { fullText: 'Valid text', blocks: [], width: 800, height: 600 },
        ],
        errors: [],
      });
      
      // Mock concatenateOcrText to fail
      mockConcatenateOcrText.mockImplementation(() => {
        throw new Error('Cannot read property map of undefined');
      });

      const result = await createDocumentWithOcr(mockImageUris, { skipOcr: false });

      expect(result).toBeDefined();
      expect(result.ocrProcessed).toBe(false); // Should be false due to concatenation error
      expect(result.ocrText).toBe(''); // Should fallback to empty string
      expect(result.uri).toContain('document_'); // PDF should still be generated
    });

    it('should handle undefined OCR results gracefully', async () => {
      mockPerformOcrOnImages.mockResolvedValue({
        results: undefined as any,
        errors: [],
      });

      const result = await createDocumentWithOcr(mockImageUris, { skipOcr: false });

      expect(result).toBeDefined();
      expect(result.ocrProcessed).toBe(false);
      expect(result.ocrText).toBe('');
      expect(result.uri).toContain('document_');
    });

    it('should not re-throw non-cancellation OCR errors', async () => {
      mockPerformOcrOnImages.mockRejectedValue(new Error('Network timeout'));

      // Should not throw - should continue with PDF only
      const result = await createDocumentWithOcr(mockImageUris, { skipOcr: false });

      expect(result).toBeDefined();
      expect(result.ocrProcessed).toBe(false);
      expect(result.uri).toContain('document_');
    });

    it('should re-throw cancellation errors from OCR', async () => {
      mockPerformOcrOnImages.mockRejectedValue(new Error('OCR processing was cancelled by user'));

      await expect(createDocumentWithOcr(mockImageUris, { skipOcr: false }))
        .rejects.toThrow('OCR processing was cancelled by user');
    });
  });

  describe('File System Error Handling', () => {
    it('should provide detailed error when PDF file verification fails', async () => {
      mockFileSystem.getInfoAsync.mockResolvedValue({
        exists: false,
        size: 0,
        isDirectory: false,
        uri: mockPdfPath,
        modificationTime: 0,
      });

      await expect(createDocumentWithOcr(mockImageUris))
        .rejects.toThrow('Generated PDF file does not exist at path:');
    });

    it('should handle file system access errors gracefully', async () => {
      mockFileSystem.getInfoAsync.mockRejectedValue(new Error('Permission denied'));

      await expect(createDocumentWithOcr(mockImageUris))
        .rejects.toThrow('Generated PDF file verification failed: Permission denied');
    });
  });

  describe('Progress Reporting with OCR Failures', () => {
    it('should report progress correctly when OCR fails', async () => {
      const progressCallback = jest.fn();
      mockPerformOcrOnImages.mockRejectedValue(new Error('OCR failed'));

      const result = await createDocumentWithOcr(mockImageUris, {
        skipOcr: false,
        onProgress: progressCallback,
      });

      expect(result).toBeDefined();
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          step: 'Text extraction failed, continuing with PDF only',
          percentage: 80,
        })
      );
    });

    it('should report correct progress when OCR is skipped', async () => {
      const progressCallback = jest.fn();

      const result = await createDocumentWithOcr(mockImageUris, {
        skipOcr: true,
        onProgress: progressCallback,
      });

      expect(result).toBeDefined();
      expect(progressCallback).toHaveBeenCalledWith(
        expect.objectContaining({
          step: 'PDF generation complete',
          percentage: 80,
        })
      );
    });
  });

  describe('OCR Text Concatenation Robustness', () => {
    it('should handle null/undefined OCR pages array', () => {
      // Test the actual concatenateOcrText function with mocked implementation
      const actualConcatenateOcrText = jest.requireActual('../utils/ocr').concatenateOcrText;
      
      expect(actualConcatenateOcrText(null)).toBe('');
      expect(actualConcatenateOcrText(undefined)).toBe('');
      expect(actualConcatenateOcrText([])).toBe('');
    });

    it('should handle invalid OCR page objects', () => {
      const actualConcatenateOcrText = jest.requireActual('../utils/ocr').concatenateOcrText;
      
      const invalidPages = [
        null,
        undefined,
        { fullText: 'Valid text' },
        { invalidProperty: 'test' },
        'not an object',
      ];
      
      expect(actualConcatenateOcrText(invalidPages)).toBe('Valid text');
    });

    it('should filter out empty text pages', () => {
      const actualConcatenateOcrText = jest.requireActual('../utils/ocr').concatenateOcrText;
      
      const pages = [
        { fullText: '  ', blocks: [] },
        { fullText: 'Valid text 1', blocks: [] },
        { fullText: '', blocks: [] },
        { fullText: 'Valid text 2', blocks: [] },
      ];
      
      expect(actualConcatenateOcrText(pages)).toBe('Valid text 1\n\nValid text 2');
    });
  });
});
