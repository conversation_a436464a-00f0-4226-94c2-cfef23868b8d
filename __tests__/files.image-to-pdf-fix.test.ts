import { getScannedDocumentName, validateImageFormat, generatePdfFromImages } from '@/utils/files';
import * as FileSystem from 'expo-file-system';

// Mock dependencies
jest.mock('expo-file-system');
jest.mock('react-native-pdf-from-image', () => ({
  createPdf: jest.fn(),
}));

const mockFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;
const mockCreatePdf = require('react-native-pdf-from-image').createPdf;

describe('Image-to-PDF Conversion Fixes', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    mockFileSystem.documentDirectory = 'file:///documents/';
    mockCreatePdf.mockResolvedValue(undefined);
  });

  describe('Document Name Generation', () => {
    it('should generate valid document names even when toLocaleDateString returns undefined', () => {
      // Mock toLocaleDateString to return undefined (simulating the bug)
      const originalToLocaleDateString = Date.prototype.toLocaleDateString;
      Date.prototype.toLocaleDateString = jest.fn().mockReturnValue(undefined);

      const documentName = getScannedDocumentName();
      
      expect(documentName).toBeDefined();
      expect(documentName).toMatch(/^Scanned_Document_\d{4}-\d{2}-\d{2}\.pdf$/);
      expect(documentName).not.toContain('undefined');

      // Restore original method
      Date.prototype.toLocaleDateString = originalToLocaleDateString;
    });

    it('should handle locale date formatting errors gracefully', () => {
      // Mock toLocaleDateString to throw an error
      const originalToLocaleDateString = Date.prototype.toLocaleDateString;
      Date.prototype.toLocaleDateString = jest.fn().mockImplementation(() => {
        throw new Error('Locale not supported');
      });

      const documentName = getScannedDocumentName();
      
      expect(documentName).toBeDefined();
      expect(documentName).toMatch(/^Scanned_Document_\d{4}-\d{2}-\d{2}\.pdf$/);

      // Restore original method
      Date.prototype.toLocaleDateString = originalToLocaleDateString;
    });

    it('should use locale date when available', () => {
      const documentName = getScannedDocumentName();
      
      expect(documentName).toBeDefined();
      expect(documentName.startsWith('Scanned_Document_')).toBe(true);
      expect(documentName.endsWith('.pdf')).toBe(true);
    });
  });

  describe('Image Format Validation', () => {
    it('should validate supported image formats', () => {
      const testCases = [
        { uri: 'file:///path/image.jpg', expected: true, format: 'jpg' },
        { uri: 'file:///path/image.jpeg', expected: true, format: 'jpeg' },
        { uri: 'file:///path/image.png', expected: true, format: 'png' },
        { uri: 'file:///path/image.heic', expected: true, format: 'heic' },
        { uri: 'data:image/jpeg;base64,/9j/4AAQ...', expected: true, format: 'jpeg' },
        { uri: 'data:image/png;base64,iVBORw0KGgo...', expected: true, format: 'png' },
        { uri: 'content://media/external/images/media/123', expected: true, format: 'unknown' },
      ];

      testCases.forEach(({ uri, expected, format }) => {
        const result = validateImageFormat(uri);
        expect(result.isValid).toBe(expected);
        if (expected) {
          expect(result.format).toBe(format);
        }
      });
    });

    it('should reject unsupported formats', () => {
      const unsupportedUris = [
        'file:///path/document.pdf',
        'file:///path/video.mp4',
        'data:text/plain;base64,SGVsbG8=',
        'file:///path/without-extension',
        '',
        null,
        undefined,
      ];

      unsupportedUris.forEach(uri => {
        const result = validateImageFormat(uri as any);
        expect(result.isValid).toBe(false);
        expect(result.error).toBeDefined();
      });
    });
  });

  describe('PDF Generation with Validation', () => {
    it('should validate image URIs before processing', async () => {
      const invalidUris = ['', null, undefined, 'invalid-uri'];
      
      await expect(generatePdfFromImages(invalidUris as any))
        .rejects.toThrow('Invalid image URIs detected');
    });

    it('should validate image formats before processing', async () => {
      const unsupportedUris = ['file:///path/document.pdf', 'file:///path/video.mp4'];
      
      await expect(generatePdfFromImages(unsupportedUris))
        .rejects.toThrow('Unsupported image formats detected');
    });

    it('should process valid images successfully', async () => {
      const validUris = [
        'file:///path/image1.jpg',
        'file:///path/image2.png',
        'data:image/jpeg;base64,/9j/4AAQ...'
      ];

      mockFileSystem.writeAsStringAsync = jest.fn().mockResolvedValue(undefined);
      mockFileSystem.getInfoAsync = jest.fn().mockResolvedValue({ exists: true, size: 1024 });

      const result = await generatePdfFromImages(validUris);
      
      expect(result).toBeDefined();
      expect(result.endsWith('.pdf')).toBe(true);
      expect(mockCreatePdf).toHaveBeenCalledWith(
        expect.objectContaining({
          imagePaths: expect.any(Array),
          name: expect.stringContaining('.pdf'),
          paperSize: 'A4'
        })
      );
    });
  });

  describe('Error Context Improvement', () => {
    it('should provide detailed error messages for debugging', async () => {
      const mixedUris = [
        'file:///valid/image.jpg',
        'file:///invalid/document.pdf',
        '',
        'file:///another/valid.png'
      ];

      try {
        await generatePdfFromImages(mixedUris);
        fail('Should have thrown an error');
      } catch (error) {
        // Should catch either invalid URIs or unsupported formats
        expect(error.message).toMatch(/Invalid image URIs detected|Unsupported image formats detected/);
      }
    });
  });
});
