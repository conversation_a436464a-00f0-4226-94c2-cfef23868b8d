import React from "react";
import { render, waitFor } from "@testing-library/react-native";
import { Platform } from "react-native";
import PDFViewer from "@/components/pdf/PDFViewer";

// Mock react-native-pdf for native PDF viewer tests
jest.mock('react-native-pdf');

// Mock file utilities
jest.mock('@/utils/files', () => ({
  ...jest.requireActual('@/utils/files'),
  convertBase64ToBlobUri: jest.fn().mockResolvedValue('file:///cache/temp_pdf_123.pdf'),
}));

// Adjust mocks for PDFViewerScreen test
const routerMod = require("expo-router");
(routerMod.useLocalSearchParams as jest.Mock).mockReturnValue({ documentId: "doc-1" });

jest.mock("@/hooks/useDocuments", () => ({
  useDocuments: () => ({
    documents: [
      {
        id: "doc-1",
        name: "Test PDF Document.pdf",
        // Data URL prefix ensures isDataUrl=true and isImage=false
        uri: "data:application/pdf;base64,QUJDREVGR0hJSktMTU5PUFFSU1RVVldY",
        size: 988,
        createdAt: new Date().toISOString(),
        isScanned: false,
      },
    ],
    deleteDocument: jest.fn(),
  }),
}));

import PDFViewerScreen from "@/app/(tabs)/documents/viewer";

describe("PDFViewer Component Platform-Aware Behavior", () => {
  const mockPdfSource = {
    kind: 'pdf' as const,
    data: 'JVBERi0xLjQKJcOkw7zDtsO4CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUgo+PgpzdHJlYW0KQNP...',
    name: 'test.pdf',
  };

  const mockImageSource = {
    kind: 'image' as const,
    data: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
    name: 'test.jpg',
  };

  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe("Platform Detection", () => {
    it("should use NativePDF on iOS for PDF sources", async () => {
      (Platform as any).OS = 'ios';

      const { getByTestId } = render(
        <PDFViewer source={mockPdfSource} />
      );

      await waitFor(() => {
        expect(getByTestId('mock-pdf-component')).toBeTruthy();
      });
    });

    it("should use NativePDF on Android for PDF sources", async () => {
      (Platform as any).OS = 'android';

      const { getByTestId } = render(
        <PDFViewer source={mockPdfSource} />
      );

      await waitFor(() => {
        expect(getByTestId('mock-pdf-component')).toBeTruthy();
      });
    });

    it("should render WebView on web for PDF sources", () => {
      (Platform as any).OS = 'web';

      const { queryByText } = render(
        <PDFViewer source={mockPdfSource} />
      );

      // Should not show unsupported message anymore
      expect(queryByText('PDF Viewer Not Available')).toBeNull();
      expect(queryByText(/Native PDF viewer is not supported on web platform/)).toBeNull();
    });

    it("should handle image sources on mobile platforms", () => {
      (Platform as any).OS = 'ios';

      const { queryByTestId } = render(
        <PDFViewer source={mockImageSource} />
      );

      // Should not render the native PDF component for image sources
      expect(queryByTestId('mock-pdf-component')).toBeNull();
    });
  });

  describe("Props Forwarding", () => {
    it("should forward all props to NativePDF on mobile", async () => {
      (Platform as any).OS = 'ios';

      const mockOnReady = jest.fn();
      const mockOnError = jest.fn();
      const mockOnMetrics = jest.fn();

      render(
        <PDFViewer
          source={mockPdfSource}
          initialZoom={1.5}
          rotation={90}
          onReady={mockOnReady}
          onError={mockOnError}
          onMetrics={mockOnMetrics}
          showTextOverlay={true}
        />
      );

      await waitFor(() => {
        expect(mockOnReady).toHaveBeenCalled();
        expect(mockOnMetrics).toHaveBeenCalledWith(
          expect.objectContaining({
            scale: 1.5,
            rotation: 90,
          })
        );
      });
    });
  });

  describe("Ref Methods", () => {
    it("should forward ref methods to NativePDF", async () => {
      (Platform as any).OS = 'ios';

      const ref = React.createRef<any>();

      render(
        <PDFViewer ref={ref} source={mockPdfSource} />
      );

      await waitFor(() => {
        expect(ref.current).toBeTruthy();
        expect(typeof ref.current.zoomIn).toBe('function');
        expect(typeof ref.current.zoomOut).toBe('function');
        expect(typeof ref.current.rotate).toBe('function');
        expect(typeof ref.current.fitWidth).toBe('function');
      });
    });
  });
});

describe("PDFViewerScreen", () => {
  beforeEach(() => {
    // Reset platform to a mobile platform for screen tests
    (Platform as any).OS = 'ios';
  });

  it("renders without re-render loop for data URL PDF", () => {
    const { getByText } = render(<PDFViewerScreen />);
    // While loading, it shows a loading UI or document title later; no crash means success.
    // We avoid strict assertions on WebView internals due to mocks.
    expect(true).toBe(true);
  });
});

