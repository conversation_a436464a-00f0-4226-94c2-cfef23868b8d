import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import { Platform } from 'react-native';
import { NativePDF } from '@/components/pdf/NativePDF';
import * as files from '@/utils/files';

// Mock dependencies
jest.mock('react-native-pdf', () => {
  const mockReact = require('react');
  const MockPdf = mockReact.forwardRef((props: any, ref: any) => {
    const renderCount = mockReact.useRef(0);
    renderCount.current++;

    mockReact.useImperativeHandle(ref, () => ({}));

    // Track component lifecycle
    mockReact.useEffect(() => {
      (global as any).nativePdfRenderCount = renderCount.current;
      (global as any).lastNativePdfProps = props;

      // Simulate PDF load after delay
      if (props.source && props.onLoadComplete) {
        const timer = setTimeout(() => {
          props.onLoadComplete(3, '/mock/path.pdf', { width: 800, height: 600 });
        }, 100);
        return () => clearTimeout(timer);
      }
    }, [props.source]);

    return mockReact.createElement('View', {
      testID: 'native-pdf-component',
      'data-render-count': renderCount.current
    });
  });
  MockPdf.displayName = 'MockPdf';
  return MockPdf;
});

jest.mock('@/utils/files');
const mockFiles = files as jest.Mocked<typeof files>;

describe('NativePDF Stability Tests', () => {
  let fileCreationCount: number;
  let createdFiles: string[];

  beforeEach(() => {
    jest.clearAllMocks();
    fileCreationCount = 0;
    createdFiles = [];
    (global as any).nativePdfRenderCount = 0;
    (global as any).lastNativePdfProps = null;
    
    // Mock Platform as Android
    Object.defineProperty(Platform, 'OS', {
      writable: true,
      value: 'android',
    });

    // Mock file conversion with tracking
    mockFiles.convertBase64ToBlobUri.mockImplementation(async (base64: string) => {
      fileCreationCount++;
      const filePath = `file:///cache/temp_pdf_${Date.now()}_${fileCreationCount}.pdf`;
      createdFiles.push(filePath);
      return filePath;
    });
  });

  describe('Component Stability', () => {
    it('should not recreate component for same source data', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      const source = { kind: 'pdf' as const, data: testData };
      
      const { rerender, getByTestId } = render(
        <NativePDF source={source} />
      );

      await waitFor(() => {
        expect(getByTestId('native-pdf-component')).toBeTruthy();
      });

      const initialRenderCount = (global as any).nativePdfRenderCount;
      const initialElement = getByTestId('native-pdf-component');

      // Multiple re-renders with same source
      for (let i = 0; i < 5; i++) {
        rerender(<NativePDF source={source} />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });
      }

      // Component should remain stable
      expect(getByTestId('native-pdf-component')).toBe(initialElement);
      expect((global as any).nativePdfRenderCount).toBeLessThanOrEqual(initialRenderCount + 1);
      expect(fileCreationCount).toBe(1);
    });

    it('should handle source changes without excessive re-creation', async () => {
      const testData1 = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      const testData2 = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMyAwIFIKPj4KZW5kb2JqCg==';
      
      const { rerender } = render(
        <NativePDF source={{ kind: 'pdf', data: testData1 }} />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      // Change source
      rerender(<NativePDF source={{ kind: 'pdf', data: testData2 }} />);

      await waitFor(() => {
        expect(fileCreationCount).toBe(2);
      });

      // Should have created exactly 2 files (one for each source)
      expect(createdFiles).toHaveLength(2);
      expect(mockFiles.convertBase64ToBlobUri).toHaveBeenCalledTimes(2);
    });

    it('should prevent infinite re-render loops', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      
      const { rerender } = render(
        <NativePDF source={{ kind: 'pdf', data: testData }} />
      );

      await waitFor(() => {
        expect((global as any).nativePdfRenderCount).toBeGreaterThan(0);
      });

      const initialRenderCount = (global as any).nativePdfRenderCount;

      // Rapid re-renders that could trigger infinite loops
      for (let i = 0; i < 10; i++) {
        rerender(<NativePDF source={{ kind: 'pdf', data: testData }} />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 20));
        });
      }

      const finalRenderCount = (global as any).nativePdfRenderCount;
      
      // Should not cause excessive re-renders
      expect(finalRenderCount - initialRenderCount).toBeLessThanOrEqual(3);
      expect(fileCreationCount).toBe(1); // Should only create one file
    });
  });

  describe('Loading State Management', () => {
    it('should handle loading state transitions correctly', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      
      const { getByText, queryByText } = render(
        <NativePDF source={{ kind: 'pdf', data: testData }} />
      );

      // Should show loading initially
      expect(getByText('Loading PDF...')).toBeTruthy();

      // Wait for load completion
      await waitFor(() => {
        expect(queryByText('Loading PDF...')).toBeNull();
      }, { timeout: 5000 });
    });

    it('should not get stuck in loading state', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      
      const { queryByText } = render(
        <NativePDF source={{ kind: 'pdf', data: testData }} />
      );

      // Wait for timeout mechanism to kick in (10 seconds in real implementation)
      // In tests, we'll wait for the mock to complete
      await waitFor(() => {
        expect(queryByText('Loading PDF...')).toBeNull();
      }, { timeout: 5000 });

      // Should not be stuck in loading state
      expect(queryByText('Loading PDF...')).toBeNull();
    });
  });

  describe('Error Handling Stability', () => {
    it('should handle file creation errors gracefully', async () => {
      const testData = 'invalid-base64-data';
      const onError = jest.fn();
      
      // Mock file creation failure
      mockFiles.convertBase64ToBlobUri.mockRejectedValue(new Error('File creation failed'));
      
      const { getByText } = render(
        <NativePDF source={{ kind: 'pdf', data: testData }} onError={onError} />
      );

      await waitFor(() => {
        expect(onError).toHaveBeenCalled();
      });

      // Should show error state
      expect(getByText('PDF Load Error')).toBeTruthy();
    });

    it('should recover from errors when source changes', async () => {
      const invalidData = 'invalid-base64-data';
      const validData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      const onError = jest.fn();
      
      // First call fails, second succeeds
      mockFiles.convertBase64ToBlobUri
        .mockRejectedValueOnce(new Error('File creation failed'))
        .mockResolvedValue('file:///cache/temp_pdf_success.pdf');
      
      const { rerender, getByText, queryByText } = render(
        <NativePDF source={{ kind: 'pdf', data: invalidData }} onError={onError} />
      );

      await waitFor(() => {
        expect(getByText('PDF Load Error')).toBeTruthy();
      });

      // Change to valid source
      rerender(<NativePDF source={{ kind: 'pdf', data: validData }} onError={onError} />);

      await waitFor(() => {
        expect(queryByText('PDF Load Error')).toBeNull();
      });
    });
  });

  describe('Memory Management', () => {
    it('should cleanup resources on unmount', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      
      const { unmount } = render(
        <NativePDF source={{ kind: 'pdf', data: testData }} />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      // Unmount should not throw
      expect(() => unmount()).not.toThrow();
    });

    it('should handle rapid mount/unmount without memory leaks', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      
      // Rapid mount/unmount cycles
      for (let i = 0; i < 5; i++) {
        const { unmount } = render(
          <NativePDF source={{ kind: 'pdf', data: testData }} />
        );
        
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });
        
        unmount();
      }

      // Should create files for each mount
      expect(fileCreationCount).toBe(5);
      expect(createdFiles).toHaveLength(5);
    });
  });

  describe('Prop Changes Handling', () => {
    it('should handle non-source prop changes without recreating files', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      
      const { rerender } = render(
        <NativePDF 
          source={{ kind: 'pdf', data: testData }}
          initialZoom={1.0}
          rotation={0}
        />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      // Change non-source props
      rerender(
        <NativePDF 
          source={{ kind: 'pdf', data: testData }}
          initialZoom={1.5}
          rotation={90}
        />
      );

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      // Should not create additional files
      expect(fileCreationCount).toBe(1);
    });

    it('should handle callback prop changes without affecting stability', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      
      const onReady1 = jest.fn();
      const onReady2 = jest.fn();
      
      const { rerender } = render(
        <NativePDF 
          source={{ kind: 'pdf', data: testData }}
          onReady={onReady1}
        />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      const initialRenderCount = (global as any).nativePdfRenderCount;

      // Change callback
      rerender(
        <NativePDF 
          source={{ kind: 'pdf', data: testData }}
          onReady={onReady2}
        />
      );

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      // Should not cause excessive re-renders or file recreation
      expect(fileCreationCount).toBe(1);
      expect((global as any).nativePdfRenderCount - initialRenderCount).toBeLessThanOrEqual(1);
    });
  });
});
