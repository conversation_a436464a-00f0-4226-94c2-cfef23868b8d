import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import { Platform } from 'react-native';
import PDFViewerScreen from '@/app/(tabs)/documents/viewer';
import { useDocuments } from '@/hooks/useDocuments';
import { useLocalSearchParams } from 'expo-router';
import * as FileSystem from 'expo-file-system';

// Mock dependencies
jest.mock('@/hooks/useDocuments');
jest.mock('expo-router');
jest.mock('expo-file-system');
jest.mock('@/components/pdf/PDFViewer', () => {
  const mockReact = require('react');
  const MockPDFViewer = mockReact.forwardRef((props: any, ref: any) => {
    // Track prop changes to detect unnecessary re-renders
    const renderCount = mockReact.useRef(0);
    renderCount.current++;

    mockReact.useImperativeHandle(ref, () => ({}));

    // Store props to detect reference changes
    mockReact.useEffect(() => {
      if (props.source) {
        (global as any).lastPDFViewerProps = {
          source: props.source,
          onError: props.onError,
          onReady: props.onReady,
          onMetrics: props.onMetrics,
          renderCount: renderCount.current
        };
      }
    });

    return mockReact.createElement('View', {
      testID: 'mock-pdf-viewer',
      'data-render-count': renderCount.current
    });
  });
  MockPDFViewer.displayName = 'MockPDFViewer';
  return MockPDFViewer;
});

const mockUseDocuments = useDocuments as jest.MockedFunction<typeof useDocuments>;
const mockUseLocalSearchParams = useLocalSearchParams as jest.MockedFunction<typeof useLocalSearchParams>;
const mockFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;

describe('PDFViewerScreen Memoization Tests', () => {
  const mockDocument = {
    id: '123',
    name: 'test.pdf',
    uri: 'file:///test.pdf',
    size: 1024,
    createdAt: Date.now(),
    ocrProcessed: false
  };

  const mockBase64Data = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

  beforeEach(() => {
    jest.clearAllMocks();
    (global as any).lastPDFViewerProps = null;
    
    // Mock Platform as Android
    Object.defineProperty(Platform, 'OS', {
      writable: true,
      value: 'android',
    });

    mockUseLocalSearchParams.mockReturnValue({ documentId: '123' });
    mockUseDocuments.mockReturnValue({
      documents: [mockDocument],
      deleteDocument: jest.fn(),
      updateDocument: jest.fn(),
      addDocument: jest.fn(),
      isLoading: false,
      error: null,
      refetch: jest.fn()
    });

    mockFileSystem.readAsStringAsync.mockResolvedValue(mockBase64Data);
  });

  describe('PDF Source Memoization', () => {
    it('should maintain stable PDF source reference when document unchanged', async () => {
      const { rerender } = render(<PDFViewerScreen />);

      // Wait for initial load
      await waitFor(() => {
        expect((global as any).lastPDFViewerProps?.source).toBeTruthy();
      });

      const initialSource = (global as any).lastPDFViewerProps.source;
      const initialRenderCount = (global as any).lastPDFViewerProps.renderCount;

      // Re-render with same document
      rerender(<PDFViewerScreen />);

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      // Source reference should be stable
      expect((global as any).lastPDFViewerProps.source).toBe(initialSource);
      // Render count should not increase significantly
      expect((global as any).lastPDFViewerProps.renderCount).toBeLessThanOrEqual(initialRenderCount + 1);
    });

    it('should update PDF source only when document data changes', async () => {
      const { rerender } = render(<PDFViewerScreen />);

      await waitFor(() => {
        expect((global as any).lastPDFViewerProps?.source).toBeTruthy();
      });

      const initialSource = (global as any).lastPDFViewerProps.source;

      // Change document data
      const newBase64Data = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMyAwIFIKPj4KZW5kb2JqCg==';
      mockFileSystem.readAsStringAsync.mockResolvedValue(newBase64Data);

      // Update documents to trigger re-load
      mockUseDocuments.mockReturnValue({
        documents: [{ ...mockDocument, size: 2048 }], // Changed size to trigger update
        deleteDocument: jest.fn(),
        updateDocument: jest.fn(),
        addDocument: jest.fn(),
        isLoading: false,
        error: null,
        refetch: jest.fn()
      });

      rerender(<PDFViewerScreen />);

      await waitFor(() => {
        expect((global as any).lastPDFViewerProps?.source?.data).toBe(newBase64Data);
      });

      // Source should be different now
      expect((global as any).lastPDFViewerProps.source).not.toBe(initialSource);
    });
  });

  describe('Callback Memoization', () => {
    it('should maintain stable callback references across renders', async () => {
      const { rerender } = render(<PDFViewerScreen />);

      await waitFor(() => {
        expect((global as any).lastPDFViewerProps?.onError).toBeTruthy();
      });

      const initialCallbacks = {
        onError: (global as any).lastPDFViewerProps.onError,
        onReady: (global as any).lastPDFViewerProps.onReady,
        onMetrics: (global as any).lastPDFViewerProps.onMetrics
      };

      // Multiple re-renders
      for (let i = 0; i < 3; i++) {
        rerender(<PDFViewerScreen />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });
      }

      // Callbacks should maintain stable references
      expect((global as any).lastPDFViewerProps.onError).toBe(initialCallbacks.onError);
      expect((global as any).lastPDFViewerProps.onReady).toBe(initialCallbacks.onReady);
      expect((global as any).lastPDFViewerProps.onMetrics).toBe(initialCallbacks.onMetrics);
    });

    it('should update callbacks only when dependencies change', async () => {
      const { rerender } = render(<PDFViewerScreen />);

      await waitFor(() => {
        expect((global as any).lastPDFViewerProps?.onMetrics).toBeTruthy();
      });

      const initialOnMetrics = (global as any).lastPDFViewerProps.onMetrics;

      // Re-render without changing dependencies
      rerender(<PDFViewerScreen />);

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 50));
      });

      // Callback should remain stable
      expect((global as any).lastPDFViewerProps.onMetrics).toBe(initialOnMetrics);
    });
  });

  describe('Conditional Rendering Optimization', () => {
    it('should not render PDFViewer when pdfBase64 is null', async () => {
      // Mock empty file read
      mockFileSystem.readAsStringAsync.mockRejectedValue(new Error('File not found'));

      const { queryByTestID } = render(<PDFViewerScreen />);

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200));
      });

      // PDFViewer should not be rendered
      expect(queryByTestID('mock-pdf-viewer')).toBeNull();
    });

    it('should render PDFViewer only after pdfBase64 is loaded', async () => {
      const { getByTestId } = render(<PDFViewerScreen />);

      await waitFor(() => {
        expect(getByTestId('mock-pdf-viewer')).toBeTruthy();
      });

      expect((global as any).lastPDFViewerProps?.source?.data).toBe(mockBase64Data);
    });
  });

  describe('Performance Regression Detection', () => {
    it('should not cause excessive re-renders of PDFViewer', async () => {
      const { rerender } = render(<PDFViewerScreen />);

      await waitFor(() => {
        expect((global as any).lastPDFViewerProps?.renderCount).toBeTruthy();
      });

      const initialRenderCount = (global as any).lastPDFViewerProps.renderCount;

      // Multiple re-renders of parent component
      for (let i = 0; i < 5; i++) {
        rerender(<PDFViewerScreen />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 30));
        });
      }

      const finalRenderCount = (global as any).lastPDFViewerProps.renderCount;
      
      // PDFViewer should not re-render excessively
      expect(finalRenderCount - initialRenderCount).toBeLessThanOrEqual(2);
    });

    it('should detect if memoization breaks and causes performance regression', async () => {
      const { rerender } = render(<PDFViewerScreen />);

      await waitFor(() => {
        expect((global as any).lastPDFViewerProps?.source).toBeTruthy();
      });

      const sourceReferences = [];
      const callbackReferences = [];

      // Collect references across multiple renders
      for (let i = 0; i < 5; i++) {
        rerender(<PDFViewerScreen />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });

        if ((global as any).lastPDFViewerProps) {
          sourceReferences.push((global as any).lastPDFViewerProps.source);
          callbackReferences.push((global as any).lastPDFViewerProps.onError);
        }
      }

      // All source references should be the same (memoized)
      const uniqueSources = new Set(sourceReferences);
      expect(uniqueSources.size).toBe(1);

      // All callback references should be the same (memoized)
      const uniqueCallbacks = new Set(callbackReferences);
      expect(uniqueCallbacks.size).toBe(1);
    });
  });

  describe('Style Memoization', () => {
    it('should maintain stable style references when screen dimensions unchanged', async () => {
      // This test would require mocking Dimensions and checking style props
      // For now, we'll test that the component renders without excessive re-renders
      const { rerender } = render(<PDFViewerScreen />);

      await waitFor(() => {
        expect((global as any).lastPDFViewerProps?.renderCount).toBeTruthy();
      });

      const initialRenderCount = (global as any).lastPDFViewerProps.renderCount;

      // Multiple re-renders
      for (let i = 0; i < 3; i++) {
        rerender(<PDFViewerScreen />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });
      }

      const finalRenderCount = (global as any).lastPDFViewerProps.renderCount;
      
      // Should not cause excessive re-renders due to style changes
      expect(finalRenderCount - initialRenderCount).toBeLessThanOrEqual(1);
    });
  });
});
