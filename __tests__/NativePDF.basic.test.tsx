import React from 'react';
import { render } from '@testing-library/react-native';
import { Platform } from 'react-native';
import NativePDF from '@/components/pdf/NativePDF';

// Mock file utilities
jest.mock('@/utils/files', () => ({
  convertBase64ToBlobUri: jest.fn().mockResolvedValue('file:///cache/temp_pdf_123.pdf'),
}));

describe('NativePDF Component - Basic Tests', () => {
  const mockPdfSource = {
    kind: 'pdf' as const,
    data: 'JVBERi0xLjQKJcOkw7zDtsO4CjIgMCBvYmoKPDwKL0xlbmd0aCAzIDAgUko+PgpzdHJlYW0KQNP...',
    name: 'test.pdf',
  };

  beforeEach(() => {
    jest.clearAllMocks();
    (Platform as any).OS = 'ios';
  });

  it('should show loading state initially for PDF sources', () => {
    (Platform as any).OS = 'ios';
    
    const { getByText } = render(
      <NativePDF source={mockPdfSource} />
    );

    expect(getByText('Loading PDF...')).toBeTruthy();
  });

  it('should call convertBase64ToBlobUri for PDF sources', () => {
    const { convertBase64ToBlobUri } = require('@/utils/files');
    
    render(<NativePDF source={mockPdfSource} />);
    
    expect(convertBase64ToBlobUri).toHaveBeenCalledWith(mockPdfSource.data);
  });

  it('should show unsupported message on web platform', () => {
    (Platform as any).OS = 'web';
    
    const { getByText } = render(
      <NativePDF source={mockPdfSource} />
    );

    expect(getByText('Platform Not Supported')).toBeTruthy();
    expect(getByText('Native PDF viewer is only available on iOS and Android')).toBeTruthy();
  });

  it('should show error message for image sources', () => {
    const mockImageSource = {
      kind: 'image' as const,
      data: 'data:image/jpeg;base64,/9j/4AAQSkZJRgABAQEAYABgAAD...',
      name: 'test.jpg',
    };

    const { getByText } = render(
      <NativePDF source={mockImageSource} />
    );

    expect(getByText('Image viewing not supported in native PDF viewer')).toBeTruthy();
  });

  it('should expose ref methods immediately', () => {
    const ref = React.createRef<any>();
    
    render(
      <NativePDF ref={ref} source={mockPdfSource} />
    );

    // Ref methods should be available immediately
    expect(ref.current).toBeTruthy();
    expect(typeof ref.current.zoomIn).toBe('function');
    expect(typeof ref.current.zoomOut).toBe('function');
    expect(typeof ref.current.rotate).toBe('function');
    expect(typeof ref.current.fitWidth).toBe('function');
  });

  it('should handle style prop correctly', () => {
    const customStyle = { backgroundColor: 'red' };

    const result = render(
      <NativePDF source={mockPdfSource} style={customStyle} />
    );

    // Component should render without errors with custom style
    expect(result).toBeTruthy();
  });

  it('should handle callback props without errors', () => {
    const mockOnReady = jest.fn();
    const mockOnError = jest.fn();
    const mockOnMetrics = jest.fn();

    const result = render(
      <NativePDF
        source={mockPdfSource}
        onReady={mockOnReady}
        onError={mockOnError}
        onMetrics={mockOnMetrics}
      />
    );

    // Component should render without errors with callbacks
    expect(result).toBeTruthy();
  });
});
