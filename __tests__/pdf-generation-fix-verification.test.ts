import { generatePdfFromImages } from '@/utils/files';
import * as FileSystem from 'expo-file-system';

// Mock dependencies
jest.mock('expo-file-system');
jest.mock('react-native-pdf-from-image', () => ({
  createPdf: jest.fn(),
}));

const mockFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;
const mockCreatePdf = require('react-native-pdf-from-image').createPdf;

describe('PDF Generation Fix Verification', () => {
  const sampleImagePath = '/home/<USER>/ReactExpoProjects/rork-ai-pdf-assistant/assets/sample/img-sample.jpeg';
  const sampleImageUri = `file://${sampleImagePath}`;

  beforeEach(() => {
    jest.clearAllMocks();
    mockFileSystem.documentDirectory = 'file:///documents/';
    mockFileSystem.cacheDirectory = 'file:///cache/';

    // Mock file system operations
    mockFileSystem.getInfoAsync = jest.fn().mockResolvedValue({
      exists: true,
      size: 1024000,
      isDirectory: false,
    });

    mockFileSystem.writeAsStringAsync = jest.fn().mockResolvedValue(undefined);
    mockFileSystem.copyAsync = jest.fn().mockResolvedValue(undefined);
    mockFileSystem.deleteAsync = jest.fn().mockResolvedValue(undefined);
  });

  describe('Core Fix Verification', () => {
    it('should call createPdf with correct parameters (imagePaths and name)', async () => {
      // Mock successful PDF creation
      mockCreatePdf.mockResolvedValue({ filePath: '/path/to/generated.pdf' });

      const result = await generatePdfFromImages([sampleImageUri]);

      expect(result).toBeDefined();
      expect(result.endsWith('.pdf')).toBe(true);

      // Verify the library is called with the correct parameter names
      expect(mockCreatePdf).toHaveBeenCalledWith(
        expect.objectContaining({
          imagePaths: [sampleImageUri], // NOT 'images'
          name: expect.stringContaining('.pdf'), // NOT 'outputPath'
          paperSize: 'A4'
        })
      );

      // Verify it's NOT called with the old incorrect parameters
      expect(mockCreatePdf).not.toHaveBeenCalledWith(
        expect.objectContaining({
          images: expect.anything(),
          outputPath: expect.anything()
        })
      );
    });

    it('should handle the library returning undefined gracefully', async () => {
      // Mock library returning undefined (which is valid)
      mockCreatePdf.mockResolvedValue(undefined);

      const result = await generatePdfFromImages([sampleImageUri]);

      expect(result).toBeDefined();
      expect(result.endsWith('.pdf')).toBe(true);
      expect(mockCreatePdf).toHaveBeenCalledWith(
        expect.objectContaining({
          imagePaths: [sampleImageUri],
          name: expect.stringContaining('.pdf'),
          paperSize: 'A4'
        })
      );
    });

    it('should handle the library returning filePath object', async () => {
      // Mock library returning object with filePath
      mockCreatePdf.mockResolvedValue({ filePath: '/generated/path/document.pdf' });

      const result = await generatePdfFromImages([sampleImageUri]);

      expect(result).toBeDefined();
      expect(result.endsWith('.pdf')).toBe(true);
      expect(mockCreatePdf).toHaveBeenCalledWith(
        expect.objectContaining({
          imagePaths: [sampleImageUri],
          name: expect.stringContaining('.pdf'),
          paperSize: 'A4'
        })
      );
    });
  });

  describe('Error Scenarios', () => {
    it('should handle library errors properly', async () => {
      // Mock library throwing an error
      const mockError = new Error('Library internal error');
      mockCreatePdf.mockRejectedValue(mockError);

      await expect(generatePdfFromImages([sampleImageUri]))
        .rejects.toThrow('Failed to generate PDF: Library internal error');
    });

    it('should handle the original replace error if it occurs', async () => {
      // Mock the original error that was happening
      const mockError = new Error("Cannot read property 'replace' of undefined");
      mockCreatePdf.mockRejectedValue(mockError);

      await expect(generatePdfFromImages([sampleImageUri]))
        .rejects.toThrow('Failed to generate PDF: Cannot read property \'replace\' of undefined');
    });
  });

  describe('Parameter Validation', () => {
    it('should extract filename correctly for the name parameter', async () => {
      mockCreatePdf.mockResolvedValue(undefined);

      await generatePdfFromImages([sampleImageUri], { nameOverride: 'custom-document' });

      expect(mockCreatePdf).toHaveBeenCalledWith(
        expect.objectContaining({
          name: 'custom-document.pdf'
        })
      );
    });

    it('should handle custom paper size correctly', async () => {
      mockCreatePdf.mockResolvedValue(undefined);

      await generatePdfFromImages([sampleImageUri], {
        customPaperSize: { width: 300, height: 400 }
      });

      expect(mockCreatePdf).toHaveBeenCalledWith(
        expect.objectContaining({
          imagePaths: [sampleImageUri],
          paperSize: 'A4',
          customPaperSize: { width: 300, height: 400 }
        })
      );
    });
  });
});