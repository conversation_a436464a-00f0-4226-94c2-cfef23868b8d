import * as ImagePicker from 'expo-image-picker';

// Mock expo-image-picker
jest.mock('expo-image-picker', () => ({
  launchImageLibraryAsync: jest.fn(),
  requestMediaLibraryPermissionsAsync: jest.fn(),
}));

const mockImagePicker = ImagePicker as jest.Mocked<typeof ImagePicker>;

describe('ImagePicker API Fix Verification', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('API Usage Verification', () => {
    it('should use array format for mediaTypes instead of enum', async () => {
      // Mock successful permission and picker response
      mockImagePicker.requestMediaLibraryPermissionsAsync.mockResolvedValue({
        status: 'granted' as any,
        expires: 'never',
        granted: true,
        canAskAgain: true,
      });

      mockImagePicker.launchImageLibraryAsync.mockResolvedValue({
        canceled: false,
        assets: [
          { 
            uri: 'file://test-image.jpg', 
            width: 800, 
            height: 600,
            type: 'image' as any,
            fileName: 'test-image.jpg',
            fileSize: 1024000,
          }
        ],
      });

      // Test the correct API usage
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'], // This should work with the new API
        quality: 1,
        allowsMultipleSelection: true,
      });

      expect(mockImagePicker.launchImageLibraryAsync).toHaveBeenCalledWith({
        mediaTypes: ['images'], // Array format, not enum
        quality: 1,
        allowsMultipleSelection: true,
      });

      expect(result.canceled).toBe(false);
      expect(result.assets).toHaveLength(1);
      expect(result.assets![0].uri).toBe('file://test-image.jpg');
    });

    it('should handle multiple media types correctly', async () => {
      mockImagePicker.launchImageLibraryAsync.mockResolvedValue({
        canceled: false,
        assets: [
          { 
            uri: 'file://test-video.mp4', 
            width: 1920, 
            height: 1080,
            type: 'video' as any,
            fileName: 'test-video.mp4',
            fileSize: 5024000,
            duration: 30000,
          }
        ],
      });

      // Test with multiple media types
      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images', 'videos'], // Array with multiple types
        quality: 1,
        allowsMultipleSelection: false,
      });

      expect(mockImagePicker.launchImageLibraryAsync).toHaveBeenCalledWith({
        mediaTypes: ['images', 'videos'], // Array format
        quality: 1,
        allowsMultipleSelection: false,
      });

      expect(result.canceled).toBe(false);
      expect(result.assets![0].type).toBe('video');
    });

    it('should handle permission requests correctly', async () => {
      mockImagePicker.requestMediaLibraryPermissionsAsync.mockResolvedValue({
        status: 'granted' as any,
        expires: 'never',
        granted: true,
        canAskAgain: true,
      });

      const permission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      expect(mockImagePicker.requestMediaLibraryPermissionsAsync).toHaveBeenCalled();
      expect(permission.status).toBe('granted');
      expect(permission.granted).toBe(true);
    });

    it('should handle canceled picker result', async () => {
      mockImagePicker.launchImageLibraryAsync.mockResolvedValue({
        canceled: true,
        assets: null,
      });

      const result = await ImagePicker.launchImageLibraryAsync({
        mediaTypes: ['images'],
        quality: 1,
        allowsMultipleSelection: true,
      });

      expect(result.canceled).toBe(true);
      expect(result.assets).toBeNull();
    });

    it('should handle permission denied', async () => {
      mockImagePicker.requestMediaLibraryPermissionsAsync.mockResolvedValue({
        status: 'denied' as any,
        expires: 'never',
        granted: false,
        canAskAgain: true,
      });

      const permission = await ImagePicker.requestMediaLibraryPermissionsAsync();

      expect(permission.status).toBe('denied');
      expect(permission.granted).toBe(false);
    });
  });

  describe('Backward Compatibility', () => {
    it('should not use deprecated MediaTypeOptions enum', () => {
      // This test ensures we are not using the deprecated enum
      const validMediaTypes = ['images', 'videos', 'livePhotos'];
      
      // Test that we can use the array format
      expect(Array.isArray(validMediaTypes)).toBe(true);
      expect(validMediaTypes).toContain('images');
      expect(validMediaTypes).toContain('videos');
    });

    it('should not use deprecated allowsEditing with allowsMultipleSelection', () => {
      // According to the docs, allowsEditing and allowsMultipleSelection are mutually exclusive
      // Our implementation should not use allowsEditing when allowsMultipleSelection is true
      
      const correctOptions = {
        mediaTypes: ['images'],
        quality: 1,
        allowsMultipleSelection: true,
        // allowsEditing should NOT be present when allowsMultipleSelection is true
      };

      expect(correctOptions).not.toHaveProperty('allowsEditing');
      expect(correctOptions.allowsMultipleSelection).toBe(true);
    });
  });
});
