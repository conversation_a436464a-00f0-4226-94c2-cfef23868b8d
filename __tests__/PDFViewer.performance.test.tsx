import React from 'react';
import { render, act, waitFor } from '@testing-library/react-native';
import { Platform } from 'react-native';
import PDFViewer from '@/components/pdf/PDFViewer';
import * as files from '@/utils/files';

// Mock dependencies
jest.mock('react-native-pdf', () => {
  const mockReact = require('react');
  const MockPdf = mockReact.forwardRef((props: any, ref: any) => {
    mockReact.useImperativeHandle(ref, () => ({}));

    // Simulate PDF load completion after a short delay
    mockReact.useEffect(() => {
      if (props.source && props.onLoadComplete) {
        console.log('[MOCK PDF] Triggering onLoadComplete for source:', props.source);
        const timer = setTimeout(() => {
          console.log('[MOCK PDF] Calling onLoadComplete callback');
          props.onLoadComplete(3, '/mock/path.pdf', { width: 800, height: 600 });
        }, 50); // Reduced timeout for faster tests
        return () => clearTimeout(timer);
      } else {
        console.log('[MOCK PDF] Not triggering onLoadComplete - source:', !!props.source, 'callback:', !!props.onLoadComplete);
      }
    }, [props.source, props.onLoadComplete]);

    // Also trigger onPageChanged for initial page
    mockReact.useEffect(() => {
      if (props.source && props.onPageChanged) {
        const timer = setTimeout(() => {
          props.onPageChanged(1, 3); // page 1 of 3
        }, 60);
        return () => clearTimeout(timer);
      }
    }, [props.source, props.onPageChanged]);

    return mockReact.createElement('View', { testID: 'mock-pdf' });
  });
  MockPdf.displayName = 'MockPdf';
  return MockPdf;
});

jest.mock('@/utils/files');
const mockFiles = files as jest.Mocked<typeof files>;

// Mock Platform
const originalPlatform = Platform.OS;

describe('PDFViewer Performance & Stability Tests', () => {
  let mockConvertBase64ToBlobUri: jest.SpyInstance;
  let fileCreationCount: number = 0;
  let createdFiles: Map<string, string> = new Map(); // Track files by hash for cache testing

  // Helper function to reset cache state for specific tests that need isolation
  const resetCacheState = () => {
    fileCreationCount = 0;
    createdFiles.clear();
  };

  beforeEach(() => {
    jest.clearAllMocks();

    // Mock Platform as Android for native PDF testing
    Object.defineProperty(Platform, 'OS', {
      writable: true,
      value: 'android',
    });

    // Mock file conversion with tracking that simulates our caching behavior
    mockConvertBase64ToBlobUri = jest.spyOn(mockFiles, 'convertBase64ToBlobUri')
      .mockImplementation(async (base64: string) => {
        // Simulate the same hash algorithm as our real implementation
        let hash = 0;
        for (let i = 0; i < Math.min(base64.length, 1000); i++) {
          const char = base64.charCodeAt(i);
          hash = ((hash << 5) - hash) + char;
          hash = hash & hash; // Convert to 32-bit integer
        }
        const hashKey = `pdf_${Math.abs(hash)}_${base64.length}`;

        if (createdFiles.has(hashKey)) {
          // Simulate cache hit - return existing file without incrementing counter
          console.log('[TEST] Cache hit for hash:', hashKey);
          return createdFiles.get(hashKey)!;
        }

        // Simulate cache miss - create new file
        fileCreationCount++;
        const filePath = `file:///cache/temp_pdf_${Date.now()}_${fileCreationCount}.pdf`;
        createdFiles.set(hashKey, filePath);
        console.log('[TEST] Cache miss, created file:', filePath, 'for hash:', hashKey);
        return filePath;
      });
  });

  afterEach(() => {
    Object.defineProperty(Platform, 'OS', {
      writable: true,
      value: originalPlatform,
    });
  });

  describe('Memoization Stability', () => {
    it('should maintain stable PDF source reference when data unchanged', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      const source = { kind: 'pdf' as const, data: testData };

      const { rerender } = render(
        <PDFViewer source={source} />
      );

      // Wait for initial render and file creation
      await waitFor(() => {
        expect(mockConvertBase64ToBlobUri).toHaveBeenCalled();
      });

      const initialCallCount = mockConvertBase64ToBlobUri.mock.calls.length;
      const initialFileCount = fileCreationCount;

      // Re-render with same source - should reuse cached file (no new file creation)
      rerender(<PDFViewer source={source} />);

      // Wait a bit to ensure no additional calls
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200));
      });

      // With caching, the mock may be called again but fileCreationCount should not increase
      expect(fileCreationCount).toBe(initialFileCount); // No new files created due to caching
    });

    it('should update PDF source reference only when data changes', async () => {
      const testData1 = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      const testData2 = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMyAwIFIKPj4KZW5kb2JqCg==';

      const { rerender } = render(
        <PDFViewer source={{ kind: 'pdf', data: testData1 }} />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      // Change data - should create new file (different hash)
      rerender(<PDFViewer source={{ kind: 'pdf', data: testData2 }} />);

      await waitFor(() => {
        expect(fileCreationCount).toBe(2); // Two different files for two different data sets
      });

      // Verify both data sets were processed
      expect(mockConvertBase64ToBlobUri).toHaveBeenCalledWith(testData1);
      expect(mockConvertBase64ToBlobUri).toHaveBeenCalledWith(testData2);
    });
  });

  describe('Re-render Prevention', () => {
    it('should not recreate files for same source data', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      const source = { kind: 'pdf' as const, data: testData };

      const { rerender } = render(
        <PDFViewer source={source} />
      );

      // Wait for initial file creation
      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      const initialFileCount = fileCreationCount;

      // Multiple re-renders with same source - should reuse cached file
      for (let i = 0; i < 5; i++) {
        rerender(<PDFViewer source={source} />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });
      }

      // Should not create additional files due to caching
      expect(fileCreationCount).toBe(initialFileCount);
    });

    it('should handle rapid prop changes without excessive file creation', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      const { rerender } = render(
        <PDFViewer source={{ kind: 'pdf', data: testData }} initialZoom={1} />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      const initialFileCount = fileCreationCount;

      // Rapid prop changes that shouldn't affect source (same PDF data)
      for (let zoom = 1.1; zoom <= 2.0; zoom += 0.1) {
        rerender(
          <PDFViewer
            source={{ kind: 'pdf', data: testData }}
            initialZoom={zoom}
          />
        );
      }

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200));
      });

      // Should still only have created one file (cached for same data)
      expect(fileCreationCount).toBe(initialFileCount);
    });
  });

  describe('Performance Regression Detection', () => {
    it('should efficiently cache and reuse files for same PDF data', async () => {
      // Reset cache state for this test to ensure clean starting point
      resetCacheState();

      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      // Render multiple instances with same data - should share cached file
      const instances = [];
      for (let i = 0; i < 3; i++) {
        instances.push(
          render(<PDFViewer source={{ kind: 'pdf', data: testData }} />)
        );
      }

      // Wait for processing - with caching, only one file should be created
      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      // Re-render first instance multiple times - should continue using cached file
      const initialFileCount = fileCreationCount;
      for (let i = 0; i < 5; i++) {
        instances[0].rerender(<PDFViewer source={{ kind: 'pdf', data: testData }} />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });
      }

      // Should not create additional files due to caching
      expect(fileCreationCount).toBe(initialFileCount);
    });

    it('should prevent excessive file creation through caching', async () => {
      // Reset cache state for this test to ensure clean starting point
      resetCacheState();

      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      const { rerender } = render(
        <PDFViewer source={{ kind: 'pdf', data: testData }} />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      const initialFileCount = fileCreationCount;

      // Multiple re-renders that previously would create multiple files
      for (let i = 0; i < 10; i++) {
        rerender(<PDFViewer source={{ kind: 'pdf', data: testData }} />);
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 20));
        });
      }

      // With caching, should still only have one file
      expect(fileCreationCount).toBe(initialFileCount);
    });
  });

  describe('Callback Stability', () => {
    it('should maintain stable callback references across renders', async () => {
      // Reset cache state for this test to ensure clean starting point
      resetCacheState();

      const onError = jest.fn();
      const onReady = jest.fn();
      const onMetrics = jest.fn();

      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      const { rerender } = render(
        <PDFViewer
          source={{ kind: 'pdf', data: testData }}
          onError={onError}
          onReady={onReady}
          onMetrics={onMetrics}
        />
      );

      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 12000 }); // Extended timeout to handle NativePDF's 10-second fallback

      const initialReadyCallCount = onReady.mock.calls.length;
      const initialMetricsCallCount = onMetrics.mock.calls.length;

      // Re-render multiple times - callbacks shouldn't be called again
      for (let i = 0; i < 3; i++) {
        rerender(
          <PDFViewer
            source={{ kind: 'pdf', data: testData }}
            onError={onError}
            onReady={onReady}
            onMetrics={onMetrics}
          />
        );
        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });
      }

      // Callbacks should maintain stability (not be called again for same source)
      expect(onReady).toHaveBeenCalledTimes(initialReadyCallCount);
      expect(onMetrics).toHaveBeenCalledTimes(initialMetricsCallCount);
      expect(onError).not.toHaveBeenCalled();
    });

    it('should call callbacks only when necessary', async () => {
      // Reset cache state for this test to ensure clean starting point
      resetCacheState();

      const onReady = jest.fn();
      const onMetrics = jest.fn();

      const testData1 = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      const testData2 = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMyAwIFIKPj4KZW5kb2JqCg==';

      const { rerender } = render(
        <PDFViewer
          source={{ kind: 'pdf', data: testData1 }}
          onReady={onReady}
          onMetrics={onMetrics}
        />
      );

      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 2000 }); // Increased timeout for async PDF preparation

      // Change source - should trigger callbacks again
      rerender(
        <PDFViewer
          source={{ kind: 'pdf', data: testData2 }}
          onReady={onReady}
          onMetrics={onMetrics}
        />
      );

      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(2);
      });
    });
  });

  describe('Conditional Rendering', () => {
    it('should not render PDFViewer when source is null', () => {
      const { queryByTestId } = render(
        <PDFViewer source={null as any} />
      );

      expect(queryByTestId('mock-pdf')).toBeNull();
      expect(mockConvertBase64ToBlobUri).not.toHaveBeenCalled();
    });

    it('should handle transition from null to valid source', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      const { rerender, queryByTestId, getByTestId } = render(
        <PDFViewer source={null as any} />
      );

      // Initially should not render
      expect(queryByTestId('mock-pdf')).toBeNull();

      // Provide valid source
      rerender(<PDFViewer source={{ kind: 'pdf', data: testData }} />);

      await waitFor(() => {
        expect(getByTestId('mock-pdf')).toBeTruthy();
      });

      expect(mockConvertBase64ToBlobUri).toHaveBeenCalledTimes(1);
    });

    it('should handle transition from valid source to null', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      const { rerender, queryByTestId, getByTestId } = render(
        <PDFViewer source={{ kind: 'pdf', data: testData }} />
      );

      await waitFor(() => {
        expect(getByTestId('mock-pdf')).toBeTruthy();
      });

      // Remove source
      rerender(<PDFViewer source={null as any} />);

      expect(queryByTestId('mock-pdf')).toBeNull();
    });

    it('should handle empty or invalid source data gracefully', async () => {
      const onError = jest.fn();

      // Test with empty string
      const { rerender } = render(
        <PDFViewer
          source={{ kind: 'pdf', data: '' }}
          onError={onError}
        />
      );

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200));
      });

      // Should handle empty data gracefully
      expect(mockConvertBase64ToBlobUri).toHaveBeenCalledWith('');
    });
  });

  describe('Memory Leak Prevention', () => {
    it('should cleanup resources when component unmounts', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      const { unmount } = render(
        <PDFViewer source={{ kind: 'pdf', data: testData }} />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBeGreaterThan(0);
      });

      // Unmount should not throw errors
      expect(() => unmount()).not.toThrow();
    });

    it('should efficiently handle rapid mount/unmount cycles with caching', async () => {
      const testData = 'JVBERi0xLjQKMSAwIG9iago8CAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      // Rapid mount/unmount cycles - with caching, should reuse the same file
      for (let i = 0; i < 5; i++) {
        const { unmount } = render(
          <PDFViewer source={{ kind: 'pdf', data: testData }} />
        );

        await act(async () => {
          await new Promise(resolve => setTimeout(resolve, 50));
        });

        unmount();
      }

      // With caching, should only create one file that gets reused
      expect(fileCreationCount).toBe(1);
    });
  });

  describe('Caching System Validation', () => {
    it('should create different files for different PDF data', async () => {
      // Reset cache state for this test to ensure clean starting point
      resetCacheState();

      const testData1 = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';
      const testData2 = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMyAwIFIKPj4KZW5kb2JqCg==';
      const testData3 = 'Different PDF content entirely';

      // Render with first data
      const { rerender } = render(
        <PDFViewer source={{ kind: 'pdf', data: testData1 }} />
      );

      await waitFor(() => {
        expect(fileCreationCount).toBe(1);
      });

      // Render with second data - should create new file
      rerender(<PDFViewer source={{ kind: 'pdf', data: testData2 }} />);

      await waitFor(() => {
        expect(fileCreationCount).toBe(2);
      });

      // Render with third data - should create another new file
      rerender(<PDFViewer source={{ kind: 'pdf', data: testData3 }} />);

      await waitFor(() => {
        expect(fileCreationCount).toBe(3);
      });

      // Go back to first data - should reuse cached file
      rerender(<PDFViewer source={{ kind: 'pdf', data: testData1 }} />);

      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 100));
      });

      // Should still be 3 files (reused first one)
      expect(fileCreationCount).toBe(3);
    });

    it('should handle identical data from different sources', async () => {
      // Reset cache state for this test to ensure clean starting point
      resetCacheState();

      const testData = 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==';

      // Create multiple components with identical data
      const component1 = render(<PDFViewer source={{ kind: 'pdf', data: testData }} />);
      const component2 = render(<PDFViewer source={{ kind: 'pdf', data: testData }} />);
      const component3 = render(<PDFViewer source={{ kind: 'pdf', data: testData }} />);

      await waitFor(() => {
        expect(fileCreationCount).toBe(1); // All should share the same cached file
      });

      // Cleanup
      component1.unmount();
      component2.unmount();
      component3.unmount();
    });
  });
});
