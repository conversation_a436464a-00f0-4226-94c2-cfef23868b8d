/**
 * OCR Toggle UI Enhancement Test
 *
 * This test verifies the OCR toggle functionality logic and state management,
 * ensuring users can easily control OCR processing for optimal performance.
 */

import AsyncStorage from '@react-native-async-storage/async-storage';

// Mock AsyncStorage
jest.mock('@react-native-async-storage/async-storage', () => ({
  getItem: jest.fn(),
  setItem: jest.fn(),
}));

const mockedAsyncStorage = AsyncStorage as jest.Mocked<typeof AsyncStorage>;

// Mock OCR preference key
const OCR_SKIP_PREFERENCE_KEY = '@scan_skip_ocr';

// Helper function to simulate OCR preference management
class OcrPreferenceManager {
  private skipOcr: boolean = true; // Default to true for fast PDF generation

  async loadOcrPreference(): Promise<boolean> {
    try {
      const savedPreference = await mockedAsyncStorage.getItem(OCR_SKIP_PREFERENCE_KEY);
      if (savedPreference !== null) {
        this.skipOcr = JSON.parse(savedPreference);
      }
    } catch (error) {
      // Handle AsyncStorage errors gracefully - keep default value
      console.warn('Failed to load OCR preference:', error);
    }
    return this.skipOcr;
  }

  async saveOcrPreference(value: boolean): Promise<void> {
    await mockedAsyncStorage.setItem(OCR_SKIP_PREFERENCE_KEY, JSON.stringify(value));
    this.skipOcr = value;
  }

  getSkipOcr(): boolean {
    return this.skipOcr;
  }

  getOcrDescription(): string {
    return this.skipOcr
      ? 'Disabled - Fast PDF creation (~700ms)'
      : 'Enabled - Slower but searchable text (~3000ms)';
  }
}

describe('OCR Toggle UI Enhancement', () => {
  let ocrManager: OcrPreferenceManager;

  beforeEach(() => {
    jest.clearAllMocks();
    ocrManager = new OcrPreferenceManager();

    // Default AsyncStorage behavior
    mockedAsyncStorage.getItem.mockResolvedValue(null);
    mockedAsyncStorage.setItem.mockResolvedValue();
  });

  describe('Default OCR State', () => {
    test('should default to OCR disabled (skipOcr: true) for fast PDF generation', async () => {
      const skipOcr = await ocrManager.loadOcrPreference();

      expect(mockedAsyncStorage.getItem).toHaveBeenCalledWith('@scan_skip_ocr');
      expect(skipOcr).toBe(true); // OCR disabled by default
    });

    test('should show fast processing time in OCR description when disabled', async () => {
      await ocrManager.loadOcrPreference();
      const description = ocrManager.getOcrDescription();

      expect(description).toBe('Disabled - Fast PDF creation (~700ms)');
    });
  });

  describe('OCR Toggle Functionality', () => {
    test('should toggle OCR state and save preference', async () => {
      // Initially OCR is disabled
      await ocrManager.loadOcrPreference();
      expect(ocrManager.getSkipOcr()).toBe(true);
      expect(ocrManager.getOcrDescription()).toBe('Disabled - Fast PDF creation (~700ms)');

      // Toggle OCR on (skipOcr = false)
      await ocrManager.saveOcrPreference(false);

      expect(mockedAsyncStorage.setItem).toHaveBeenCalledWith('@scan_skip_ocr', 'false');
      expect(ocrManager.getSkipOcr()).toBe(false);
      expect(ocrManager.getOcrDescription()).toBe('Enabled - Slower but searchable text (~3000ms)');

      // Toggle OCR off (skipOcr = true)
      await ocrManager.saveOcrPreference(true);

      expect(mockedAsyncStorage.setItem).toHaveBeenCalledWith('@scan_skip_ocr', 'true');
      expect(ocrManager.getSkipOcr()).toBe(true);
      expect(ocrManager.getOcrDescription()).toBe('Disabled - Fast PDF creation (~700ms)');
    });

    test('should load saved OCR preference on app start', async () => {
      // Mock saved preference for OCR enabled
      mockedAsyncStorage.getItem.mockResolvedValue('false'); // skipOcr = false means OCR enabled

      const skipOcr = await ocrManager.loadOcrPreference();

      expect(mockedAsyncStorage.getItem).toHaveBeenCalledWith('@scan_skip_ocr');
      expect(skipOcr).toBe(false); // OCR enabled
      expect(ocrManager.getOcrDescription()).toBe('Enabled - Slower but searchable text (~3000ms)');
    });

    test('should handle AsyncStorage errors gracefully', async () => {
      // Mock AsyncStorage error
      mockedAsyncStorage.getItem.mockRejectedValue(new Error('Storage error'));

      // Should still return default value
      const skipOcr = await ocrManager.loadOcrPreference();
      expect(skipOcr).toBe(true); // Default value
    });
  });

  describe('Performance Indicators', () => {
    test('should show accurate processing time estimates', async () => {
      // OCR disabled - should show fast time
      await ocrManager.loadOcrPreference();
      expect(ocrManager.getOcrDescription()).toBe('Disabled - Fast PDF creation (~700ms)');

      // Enable OCR - should show slower time
      await ocrManager.saveOcrPreference(false);
      expect(ocrManager.getOcrDescription()).toBe('Enabled - Slower but searchable text (~3000ms)');
    });

    test('should provide clear performance trade-off information', () => {
      const fastDescription = 'Disabled - Fast PDF creation (~700ms)';
      const slowDescription = 'Enabled - Slower but searchable text (~3000ms)';

      // Verify descriptions contain performance information
      expect(fastDescription).toContain('~700ms');
      expect(slowDescription).toContain('~3000ms');
      expect(slowDescription).toContain('searchable text');
    });
  });

  describe('OCR State Logic', () => {
    test('should correctly invert skipOcr for UI toggle display', () => {
      // When skipOcr is true (OCR disabled), UI toggle should be false (OFF)
      ocrManager = new OcrPreferenceManager();
      expect(ocrManager.getSkipOcr()).toBe(true);

      // UI toggle value should be inverted: !skipOcr
      const toggleValue = !ocrManager.getSkipOcr();
      expect(toggleValue).toBe(false); // Toggle OFF when OCR disabled
    });

    test('should handle toggle value changes correctly', async () => {
      // UI toggle ON (value=true) should set skipOcr=false (enable OCR)
      const toggleValue = true;
      const skipOcrValue = !toggleValue;

      await ocrManager.saveOcrPreference(skipOcrValue);
      expect(ocrManager.getSkipOcr()).toBe(false); // OCR enabled

      // UI toggle OFF (value=false) should set skipOcr=true (disable OCR)
      const toggleValueOff = false;
      const skipOcrValueOff = !toggleValueOff;

      await ocrManager.saveOcrPreference(skipOcrValueOff);
      expect(ocrManager.getSkipOcr()).toBe(true); // OCR disabled
    });
  });

  describe('Integration with Document Creation', () => {
    test('should pass correct skipOcr value to createDocumentWithOcr', async () => {
      // Mock document creation options
      const createDocumentOptions = (skipOcr: boolean) => ({
        documentName: 'Test Document',
        skipOcr: skipOcr,
        onProgress: jest.fn(),
        cancelToken: { cancelled: false },
      });

      // OCR disabled by default
      await ocrManager.loadOcrPreference();
      let options = createDocumentOptions(ocrManager.getSkipOcr());
      expect(options.skipOcr).toBe(true); // Fast PDF creation

      // Enable OCR
      await ocrManager.saveOcrPreference(false);
      options = createDocumentOptions(ocrManager.getSkipOcr());
      expect(options.skipOcr).toBe(false); // OCR enabled
    });
  });
});
