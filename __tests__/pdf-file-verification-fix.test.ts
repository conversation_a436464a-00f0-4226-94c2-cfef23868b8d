/**
 * PDF File Verification Fix Test
 * 
 * This test verifies the enhanced PDF file verification logic that handles
 * path mismatches between expected and actual library-created file paths.
 */

import * as FileSystem from 'expo-file-system';

// Mock FileSystem
jest.mock('expo-file-system', () => ({
  documentDirectory: 'file:///documents/',
  cacheDirectory: 'file:///cache/',
  getInfoAsync: jest.fn(),
  writeAsStringAsync: jest.fn(),
  deleteAsync: jest.fn(),
  copyAsync: jest.fn(),
  EncodingType: {
    Base64: 'base64',
  },
}));

const mockedFileSystem = FileSystem as jest.Mocked<typeof FileSystem>;

// Import the function after mocks are set up
import { generatePdfFromImages } from '../utils/files';

// Get the mocked createPdf function from the global mock
const { createPdf: mockCreatePdf } = require('react-native-pdf-from-image');

describe('PDF File Verification Fix', () => {
  const sampleImageUri = 'file:///sample/image.jpg';

  beforeEach(() => {
    jest.clearAllMocks();
    
    // Reset console methods
    jest.spyOn(console, 'log').mockImplementation();
    jest.spyOn(console, 'warn').mockImplementation();
    jest.spyOn(console, 'error').mockImplementation();
    
    // Default file system mocks
    mockedFileSystem.writeAsStringAsync.mockResolvedValue();
    mockedFileSystem.deleteAsync.mockResolvedValue();
    mockedFileSystem.copyAsync.mockResolvedValue();
  });

  afterEach(() => {
    jest.restoreAllMocks();
  });

  test('should verify PDF at expected path when library creates file correctly', async () => {
    const expectedPath = 'file:///documents/test.pdf';
    
    // Mock library returning expected path
    mockCreatePdf.mockResolvedValue({
      filePath: expectedPath,
      success: true,
    });

    // Mock file exists at expected path
    mockedFileSystem.getInfoAsync.mockResolvedValue({
      exists: true,
      isDirectory: false,
      size: 1024000,
      modificationTime: Date.now(),
      uri: expectedPath,
    });

    const result = await generatePdfFromImages([sampleImageUri]);
    
    expect(result).toBe(expectedPath);
    expect(mockedFileSystem.getInfoAsync).toHaveBeenCalledWith(expectedPath);
    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining('PDF file verified:')
    );
  });

  test('should handle path mismatch and verify at library path', async () => {
    const expectedPath = 'file:///documents/test.pdf';
    const libraryPath = '/data/user/0/app.rork.aipdfassistant/files/test.pdf';
    
    // Mock library returning different path
    mockCreatePdf.mockResolvedValue({
      filePath: libraryPath,
      success: true,
    });

    // Mock file exists only at library path
    mockedFileSystem.getInfoAsync.mockImplementation((path) => {
      if (path === libraryPath) {
        return Promise.resolve({
          exists: true,
          isDirectory: false,
          size: 2048000,
          modificationTime: Date.now(),
          uri: libraryPath,
        });
      }
      return Promise.resolve({
        exists: false,
        isDirectory: false,
        size: 0,
        modificationTime: 0,
        uri: '',
      });
    });

    const result = await generatePdfFromImages([sampleImageUri]);
    
    expect(result).toBe(libraryPath);
    expect(console.warn).toHaveBeenCalledWith(
      expect.stringContaining('Path mismatch detected. Using library path:')
    );
    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining('PDF file verified:')
    );
  });

  test('should use fallback path when primary path fails', async () => {
    const expectedPath = 'file:///documents/test.pdf';

    // Mock library returning same path (no mismatch detected initially)
    mockCreatePdf.mockResolvedValue({
      filePath: expectedPath,
      success: true,
    });

    // Mock expected path doesn't exist
    mockedFileSystem.getInfoAsync.mockResolvedValue({
      exists: false,
      isDirectory: false,
      size: 0,
      modificationTime: 0,
      uri: '',
    });

    // This should throw an error since the file doesn't exist
    await expect(generatePdfFromImages([sampleImageUri])).rejects.toThrow(
      'Failed to generate PDF'
    );
  });

  test('should throw error when file does not exist at any path', async () => {
    const expectedPath = 'file:///documents/test.pdf';
    const libraryPath = '/data/user/0/app.rork.aipdfassistant/files/test.pdf';
    
    // Mock library returning different path
    mockCreatePdf.mockResolvedValue({
      filePath: libraryPath,
      success: true,
    });

    // Mock file doesn't exist at any path
    mockedFileSystem.getInfoAsync.mockResolvedValue({
      exists: false,
      isDirectory: false,
      size: 0,
      modificationTime: 0,
      uri: '',
    });

    await expect(generatePdfFromImages([sampleImageUri])).rejects.toThrow(
      'Generated PDF file verification failed'
    );
  });

  test('should handle file system errors gracefully', async () => {
    const expectedPath = 'file:///documents/test.pdf';
    
    // Mock library returning expected path
    mockCreatePdf.mockResolvedValue({
      filePath: expectedPath,
      success: true,
    });

    // Mock file system error
    mockedFileSystem.getInfoAsync.mockRejectedValue(new Error('Permission denied'));

    await expect(generatePdfFromImages([sampleImageUri])).rejects.toThrow(
      'Failed to generate PDF'
    );
    
    expect(console.error).toHaveBeenCalledWith(
      '[PDF Generation] Failed to verify PDF file:',
      expect.any(Error)
    );
  });

  test('should handle library returning undefined response', async () => {
    // Mock library returning undefined
    mockCreatePdf.mockResolvedValue(undefined);

    // Mock file exists at expected path
    mockedFileSystem.getInfoAsync.mockResolvedValue({
      exists: true,
      isDirectory: false,
      size: 512000,
      modificationTime: Date.now(),
      uri: 'file:///documents/document_123.pdf',
    });

    const result = await generatePdfFromImages([sampleImageUri]);
    
    // Should still work with expected path
    expect(result).toMatch(/document_.*\.pdf$/);
    expect(console.log).toHaveBeenCalledWith(
      '[PDF Generation] Library returned:',
      undefined
    );
  });

  test('should log detailed path information during verification', async () => {
    const expectedPath = 'file:///documents/test.pdf';
    const libraryPath = '/data/user/0/app.rork.aipdfassistant/files/test.pdf';
    
    // Mock library returning different path
    mockCreatePdf.mockResolvedValue({
      filePath: libraryPath,
      success: true,
    });

    // Mock file exists at library path
    mockedFileSystem.getInfoAsync.mockImplementation((path) => {
      if (path === libraryPath) {
        return Promise.resolve({
          exists: true,
          isDirectory: false,
          size: 3072000,
          modificationTime: Date.now(),
          uri: libraryPath,
        });
      }
      return Promise.resolve({
        exists: false,
        isDirectory: false,
        size: 0,
        modificationTime: 0,
        uri: '',
      });
    });

    await generatePdfFromImages([sampleImageUri]);
    
    // Verify detailed logging
    expect(console.log).toHaveBeenCalledWith(
      '[PDF Generation] Library returned:',
      expect.objectContaining({ filePath: libraryPath })
    );
    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining('Library created file at:')
    );
    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining('Expected file at:')
    );
    expect(console.warn).toHaveBeenCalledWith(
      expect.stringContaining('Path mismatch detected. Using library path:')
    );
    expect(console.log).toHaveBeenCalledWith(
      expect.stringContaining('PDF file verified:')
    );
  });
});
