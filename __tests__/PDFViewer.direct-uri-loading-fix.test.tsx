import React from 'react';
import { render, waitFor, act } from '@testing-library/react-native';
import { PDFViewer } from '../components/pdf/PDFViewer';
import { Platform } from 'react-native';

// Mock react-native-pdf with detailed loading simulation
jest.mock('react-native-pdf', () => {
  const mockReact = require('react');
  return mockReact.forwardRef((props: any, ref: any) => {
    const { View, Text } = require('react-native');
    
    // Simulate react-native-pdf behavior for direct file URIs
    mockReact.useEffect(() => {
      if (props.source && props.onLoadComplete) {
        console.log('[MOCK PDF] Source received:', props.source);
        
        // Simulate instant loading for direct file URIs
        if (props.source.uri && props.source.uri.startsWith('file://')) {
          console.log('[MOCK PDF] Direct file URI detected, simulating instant load');
          const timer = setTimeout(() => {
            console.log('[MOCK PDF] Calling onLoadComplete for direct file URI');
            props.onLoadComplete(3, props.source.uri, { width: 800, height: 600 });
          }, 50); // Very fast loading for direct URIs
          return () => clearTimeout(timer);
        } else {
          console.log('[MOCK PDF] Non-direct URI, simulating slower load');
          const timer = setTimeout(() => {
            console.log('[MOCK PDF] Calling onLoadComplete for non-direct URI');
            props.onLoadComplete(3, props.source.uri || 'unknown', { width: 800, height: 600 });
          }, 200);
          return () => clearTimeout(timer);
        }
      }
    }, [props.source, props.onLoadComplete]);

    return (
      <View testID="mock-pdf">
        <Text>Mock PDF - URI: {props.source?.uri}</Text>
        <Text>Cache: {props.source?.cache ? 'true' : 'false'}</Text>
      </View>
    );
  });
});

// Mock file system operations
jest.mock('expo-file-system', () => ({
  cacheDirectory: '/cache/',
  writeAsStringAsync: jest.fn().mockResolvedValue(undefined),
  getInfoAsync: jest.fn().mockResolvedValue({ exists: true }),
  deleteAsync: jest.fn().mockResolvedValue(undefined),
}));

// Mock platform for native behavior
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Platform: {
      ...RN.Platform,
      OS: 'android',
    },
  };
});

describe('PDFViewer Direct File URI Loading Fix Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();
    console.warn = jest.fn();
  });

  describe('Loading State Management Fix', () => {
    it('should properly set loading state to false for direct file URIs', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();
      const onMetrics = jest.fn();

      const directFileSource = {
        kind: 'pdf' as const,
        data: '',
        fileUri: 'file:///data/user/0/app.rork.aipdfassistant/cache/DocumentPicker/test.pdf',
        name: 'test.pdf'
      };

      const { getByTestId, queryByText } = render(
        <PDFViewer
          source={directFileSource}
          onReady={onReady}
          onError={onError}
          onMetrics={onMetrics}
        />
      );

      // Should not show loading indicator after direct file URI is processed
      await waitFor(() => {
        // Check that loading state is properly managed
        expect(console.log).toHaveBeenCalledWith(
          expect.stringContaining('[NativePDF] Loading state set to false for direct file URI')
        );
      }, { timeout: 1000 });

      // Should trigger onReady callback
      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 1000 });

      // Verify the PDF component is rendered
      expect(getByTestId('mock-pdf')).toBeTruthy();
      expect(onError).not.toHaveBeenCalled();

      // Verify optimization logs
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Using direct file URI (optimized path)')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Direct file URI source set')
      );
    });

    it('should handle sample PDF file correctly', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();

      // Test with the actual sample PDF path
      const samplePdfSource = {
        kind: 'pdf' as const,
        data: '',
        fileUri: 'file:///assets/sample/DC02.pdf',
        name: 'DC02.pdf'
      };

      const { getByTestId } = render(
        <PDFViewer
          source={samplePdfSource}
          onReady={onReady}
          onError={onError}
        />
      );

      // Should load the sample PDF successfully
      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 1000 });

      // Verify the PDF component receives the correct URI
      const mockPdfComponent = getByTestId('mock-pdf');
      expect(mockPdfComponent).toBeTruthy();

      // Verify optimization is used for sample PDF
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Using direct file URI (optimized path)')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('DC02.pdf')
      );

      expect(onError).not.toHaveBeenCalled();
    });

    it('should fallback gracefully when direct file URI fails', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();

      // Test with both direct URI and fallback data
      const hybridSource = {
        kind: 'pdf' as const,
        data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
        fileUri: 'file:///invalid/path/test.pdf', // Invalid path to trigger fallback
        name: 'test.pdf'
      };

      render(
        <PDFViewer
          source={hybridSource}
          onReady={onReady}
          onError={onError}
        />
      );

      // Should initially try direct file URI
      await waitFor(() => {
        expect(console.log).toHaveBeenCalledWith(
          expect.stringContaining('[NativePDF] Using direct file URI (optimized path)')
        );
      }, { timeout: 500 });

      // If direct URI fails, should have fallback data available
      expect(hybridSource.data).toBeTruthy();
      console.log('✅ Fallback mechanism test completed');
    });

    it('should compare loading times: direct URI vs base64', async () => {
      // Test direct file URI loading time
      const directStartTime = Date.now();
      const onReadyDirect = jest.fn(() => {
        const directEndTime = Date.now();
        console.log(`[PERF TEST] Direct URI loading time: ${directEndTime - directStartTime}ms`);
      });

      const { unmount: unmountDirect } = render(
        <PDFViewer
          source={{
            kind: 'pdf' as const,
            data: '',
            fileUri: 'file:///cache/direct-test.pdf',
            name: 'direct-test.pdf'
          }}
          onReady={onReadyDirect}
        />
      );

      await waitFor(() => {
        expect(onReadyDirect).toHaveBeenCalledTimes(1);
      }, { timeout: 1000 });

      unmountDirect();

      // Test base64 loading time
      const base64StartTime = Date.now();
      const onReadyBase64 = jest.fn(() => {
        const base64EndTime = Date.now();
        console.log(`[PERF TEST] Base64 loading time: ${base64EndTime - base64StartTime}ms`);
      });

      render(
        <PDFViewer
          source={{
            kind: 'pdf' as const,
            data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
            name: 'base64-test.pdf'
          }}
          onReady={onReadyBase64}
        />
      );

      await waitFor(() => {
        expect(onReadyBase64).toHaveBeenCalledTimes(1);
      }, { timeout: 5000 });

      // Both should complete successfully
      expect(onReadyDirect).toHaveBeenCalledTimes(1);
      expect(onReadyBase64).toHaveBeenCalledTimes(1);

      console.log('✅ Performance comparison test completed');
    });
  });

  describe('Loading State Debugging', () => {
    it('should provide detailed loading state diagnostics', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();
      const onMetrics = jest.fn();

      const diagnosticSource = {
        kind: 'pdf' as const,
        data: '',
        fileUri: 'file:///diagnostic/test.pdf',
        name: 'diagnostic.pdf'
      };

      render(
        <PDFViewer
          source={diagnosticSource}
          onReady={onReady}
          onError={onError}
          onMetrics={onMetrics}
        />
      );

      // Wait for all loading steps to complete
      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 1000 });

      // Verify diagnostic logs are present
      const expectedLogs = [
        '[NativePDF] Using direct file URI (optimized path)',
        '[NativePDF] Direct file URI source set',
        '[NativePDF] Loading state set to false for direct file URI',
        '[NativePDF] Triggering onReady for direct file URI'
      ];

      expectedLogs.forEach(expectedLog => {
        expect(console.log).toHaveBeenCalledWith(
          expect.stringContaining(expectedLog)
        );
      });

      console.log('✅ All diagnostic logs verified');
    });
  });
});
