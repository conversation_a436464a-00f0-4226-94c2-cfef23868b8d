import React from 'react';
import { render, waitFor, act } from '@testing-library/react-native';
import { PDFViewer } from '../components/pdf/PDFViewer';

// Mock react-native-pdf with crash simulation
jest.mock('react-native-pdf', () => {
  const mockReact = require('react');
  return mockReact.forwardRef((props: any, ref: any) => {
    const { View, Text } = require('react-native');
    
    // Simulate rapid page changes and scale changes
    mockReact.useEffect(() => {
      if (props.onLoadComplete) {
        const timer = setTimeout(() => {
          props.onLoadComplete(10, '/mock/path.pdf', { width: 800, height: 600 });
        }, 100);
        return () => clearTimeout(timer);
      }
    }, [props.onLoadComplete]);

    // Simulate rapid scrolling events
    mockReact.useEffect(() => {
      if (props.onPageChanged) {
        let currentPage = 1;
        const interval = setInterval(() => {
          // Simulate rapid page changes during scrolling
          currentPage = (currentPage % 10) + 1;
          props.onPageChanged(currentPage, 10);
        }, 50); // Very rapid page changes
        
        return () => clearInterval(interval);
      }
    }, [props.onPageChanged]);

    // Simulate scale changes during pinch-to-zoom
    mockReact.useEffect(() => {
      if (props.onScaleChanged) {
        let scale = 1.0;
        const interval = setInterval(() => {
          scale = 0.5 + Math.random() * 2.5; // Random scale between 0.5 and 3.0
          props.onScaleChanged(scale);
        }, 200); // Rapid scale changes
        
        return () => clearInterval(interval);
      }
    }, [props.onScaleChanged]);

    return (
      <View testID="mock-pdf">
        <Text>Mock PDF - Crash Prevention Test</Text>
        <Text>Page: {props.page}</Text>
        <Text>Scale: {props.scale}</Text>
      </View>
    );
  });
});

// Mock file system operations
jest.mock('expo-file-system', () => ({
  cacheDirectory: '/cache/',
  writeAsStringAsync: jest.fn().mockResolvedValue(undefined),
  getInfoAsync: jest.fn().mockResolvedValue({ exists: true }),
  deleteAsync: jest.fn().mockResolvedValue(undefined),
}));

describe('PDFViewer Crash Prevention Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn();
    console.warn = jest.fn();
    console.error = jest.fn();
  });

  describe('Rapid Scrolling Crash Prevention', () => {
    it('should handle rapid page changes without crashing', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();
      const onMetrics = jest.fn();

      const testSource = {
        kind: 'pdf' as const,
        data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
        name: 'crash-test.pdf'
      };

      const { getByTestId } = render(
        <PDFViewer
          source={testSource}
          onReady={onReady}
          onError={onError}
          onMetrics={onMetrics}
        />
      );

      // Wait for initial load
      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 2000 });

      // Let rapid page changes run for a while
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 1000));
      });

      // Verify component is still rendered and functional
      expect(getByTestId('mock-pdf')).toBeTruthy();
      expect(onError).not.toHaveBeenCalled();

      // Verify throttling is working (should have fewer metrics calls than page changes)
      const metricsCallCount = onMetrics.mock.calls.length;
      console.log(`[TEST] Metrics calls: ${metricsCallCount} (should be throttled)`);
      
      // Should have some metrics calls but not excessive
      expect(metricsCallCount).toBeGreaterThan(0);
      expect(metricsCallCount).toBeLessThan(50); // Should be throttled

      // Verify throttling logs
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Page changed (throttled)')
      );
    });

    it('should handle rapid scale changes without memory issues', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();
      const onMetrics = jest.fn();

      const testSource = {
        kind: 'pdf' as const,
        data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
        name: 'scale-test.pdf'
      };

      render(
        <PDFViewer
          source={testSource}
          onReady={onReady}
          onError={onError}
          onMetrics={onMetrics}
        />
      );

      // Wait for initial load
      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 2000 });

      // Let rapid scale changes run
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 1500));
      });

      // Verify no crashes occurred
      expect(onError).not.toHaveBeenCalled();

      // Verify scale change throttling
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Scale changed')
      );

      console.log('✅ Scale change throttling test completed');
    });

    it('should prevent memory leaks from excessive page metrics', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();

      // Create a source that would generate many pages
      const largeDocSource = {
        kind: 'pdf' as const,
        data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
        name: 'large-doc.pdf'
      };

      render(
        <PDFViewer
          source={largeDocSource}
          onReady={onReady}
          onError={onError}
        />
      );

      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 2000 });

      // Simulate memory pressure scenario
      await act(async () => {
        // Let the component run for a while to potentially trigger memory monitoring
        await new Promise(resolve => setTimeout(resolve, 6000));
      });

      // Verify no crashes from memory issues
      expect(onError).not.toHaveBeenCalled();

      // Check if memory pressure monitoring was triggered
      // (This would show in logs if pageMetrics grew too large)
      console.log('✅ Memory pressure monitoring test completed');
    });

    it('should handle invalid page numbers gracefully', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();

      const testSource = {
        kind: 'pdf' as const,
        data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
        name: 'invalid-page-test.pdf'
      };

      render(
        <PDFViewer
          source={testSource}
          onReady={onReady}
          onError={onError}
        />
      );

      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 2000 });

      // Let the test run to potentially trigger invalid page numbers
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 1000));
      });

      // Verify invalid page warnings are logged
      expect(console.warn).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Invalid page number')
      );

      // Verify no crashes from invalid pages
      expect(onError).not.toHaveBeenCalled();

      console.log('✅ Invalid page number handling test completed');
    });

    it('should cleanup throttle timers on unmount', async () => {
      const onReady = jest.fn();

      const testSource = {
        kind: 'pdf' as const,
        data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
        name: 'cleanup-test.pdf'
      };

      const { unmount } = render(
        <PDFViewer
          source={testSource}
          onReady={onReady}
        />
      );

      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 2000 });

      // Let some throttled events accumulate
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 500));
      });

      // Unmount component
      unmount();

      // Wait a bit more to ensure cleanup happened
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 200));
      });

      console.log('✅ Cleanup test completed - no memory leaks expected');
    });
  });

  describe('Performance Optimization Validation', () => {
    it('should use requestAnimationFrame for expensive operations', async () => {
      const onReady = jest.fn();
      const onMetrics = jest.fn();

      const testSource = {
        kind: 'pdf' as const,
        data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
        name: 'performance-test.pdf'
      };

      render(
        <PDFViewer
          source={testSource}
          onReady={onReady}
          onMetrics={onMetrics}
        />
      );

      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 2000 });

      // Let rapid events trigger requestAnimationFrame usage
      await act(async () => {
        await new Promise(resolve => setTimeout(resolve, 1000));
      });

      // Verify metrics are still being called (deferred via requestAnimationFrame)
      expect(onMetrics).toHaveBeenCalled();

      console.log('✅ requestAnimationFrame optimization test completed');
    });
  });
});
