import React from 'react';
import { render, waitFor } from '@testing-library/react-native';
import { PDFViewer } from '../components/pdf/PDFViewer';
import { Platform } from 'react-native';

// Mock react-native-pdf
jest.mock('react-native-pdf', () => {
  const mockReact = require('react');
  return mockReact.forwardRef((props: any, ref: any) => {
    const { View, Text } = require('react-native');
    
    // Simulate instant loading for direct file URIs
    mockReact.useEffect(() => {
      if (props.source && props.onLoadComplete) {
        console.log('[MOCK PDF] Direct URI detected, loading instantly:', props.source);
        const timer = setTimeout(() => {
          console.log('[MOCK PDF] Instant load complete for direct URI');
          props.onLoadComplete(3, props.source.uri, { width: 800, height: 600 });
        }, 10); // Instant loading simulation
        return () => clearTimeout(timer);
      }
    }, [props.source, props.onLoadComplete]);

    return (
      <View testID="mock-pdf">
        <Text>Mock PDF Component - Direct URI: {props.source?.uri}</Text>
      </View>
    );
  });
});

// Mock file system operations
jest.mock('expo-file-system', () => ({
  cacheDirectory: '/cache/',
  writeAsStringAsync: jest.fn().mockResolvedValue(undefined),
  getInfoAsync: jest.fn().mockResolvedValue({ exists: true }),
  deleteAsync: jest.fn().mockResolvedValue(undefined),
}));

// Mock platform to test native behavior
jest.mock('react-native', () => {
  const RN = jest.requireActual('react-native');
  return {
    ...RN,
    Platform: {
      ...RN.Platform,
      OS: 'android', // Test Android behavior
    },
  };
});

describe('PDFViewer Direct File URI Optimization Tests', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    console.log = jest.fn(); // Mock console.log to capture optimization logs
  });

  describe('Direct File URI Performance', () => {
    it('should use direct file URI path for instant loading', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();
      const onMetrics = jest.fn();

      const directFileSource = {
        kind: 'pdf' as const,
        data: '', // Empty data since we're using direct file URI
        fileUri: 'file:///data/user/0/app.rork.aipdfassistant/cache/DocumentPicker/test.pdf',
        name: 'test.pdf'
      };

      const { getByTestId } = render(
        <PDFViewer
          source={directFileSource}
          onReady={onReady}
          onError={onError}
          onMetrics={onMetrics}
        />
      );

      // Should load almost instantly with direct file URI
      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 500 }); // Much shorter timeout for direct URI

      // Verify optimization logs
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Using direct file URI (optimized path)')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Direct file URI loaded instantly')
      );

      // Should not use base64 conversion path
      expect(console.log).not.toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Using base64 conversion (legacy path)')
      );

      expect(onError).not.toHaveBeenCalled();
      expect(getByTestId('mock-pdf')).toBeTruthy();
    });

    it('should fallback to base64 conversion when no direct file URI', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();

      const base64Source = {
        kind: 'pdf' as const,
        data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
        name: 'test.pdf'
      };

      render(
        <PDFViewer
          source={base64Source}
          onReady={onReady}
          onError={onError}
        />
      );

      // Should use legacy base64 conversion path
      await waitFor(() => {
        expect(console.log).toHaveBeenCalledWith(
          expect.stringContaining('[NativePDF] Using base64 conversion (legacy path)')
        );
      }, { timeout: 1000 });

      // Should not use direct file URI path
      expect(console.log).not.toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Using direct file URI (optimized path)')
      );
    });

    it('should handle DocumentPicker file URIs correctly', async () => {
      const onReady = jest.fn();
      const onError = jest.fn();

      // Simulate DocumentPicker result
      const documentPickerSource = {
        kind: 'pdf' as const,
        data: '',
        fileUri: 'file:///data/user/0/app.rork.aipdfassistant/cache/DocumentPicker/6718847c-355c-42d5-9a7d-5620c5104673.pdf',
        name: 'document.pdf'
      };

      const { getByTestId } = render(
        <PDFViewer
          source={documentPickerSource}
          onReady={onReady}
          onError={onError}
        />
      );

      // Should load instantly without base64 conversion
      await waitFor(() => {
        expect(onReady).toHaveBeenCalledTimes(1);
      }, { timeout: 500 });

      // Verify the PDF component receives the direct URI
      const mockPdfComponent = getByTestId('mock-pdf');
      expect(mockPdfComponent).toBeTruthy();

      // Verify optimization logs
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('[NativePDF] Using direct file URI (optimized path)')
      );
      expect(console.log).toHaveBeenCalledWith(
        expect.stringContaining('DocumentPicker/6718847c-355c-42d5-9a7d-5620c5104673.pdf')
      );

      expect(onError).not.toHaveBeenCalled();
    });

    it('should compare performance: direct URI vs base64 conversion', async () => {
      const directUriStartTime = Date.now();
      const onReadyDirect = jest.fn(() => {
        const directUriEndTime = Date.now();
        console.log(`[PERF] Direct URI loading time: ${directUriEndTime - directUriStartTime}ms`);
      });

      // Test direct file URI
      const { unmount: unmountDirect } = render(
        <PDFViewer
          source={{
            kind: 'pdf' as const,
            data: '',
            fileUri: 'file:///cache/test-direct.pdf',
            name: 'test-direct.pdf'
          }}
          onReady={onReadyDirect}
        />
      );

      await waitFor(() => {
        expect(onReadyDirect).toHaveBeenCalledTimes(1);
      }, { timeout: 500 });

      unmountDirect();

      // Test base64 conversion
      const base64StartTime = Date.now();
      const onReadyBase64 = jest.fn(() => {
        const base64EndTime = Date.now();
        console.log(`[PERF] Base64 conversion loading time: ${base64EndTime - base64StartTime}ms`);
      });

      render(
        <PDFViewer
          source={{
            kind: 'pdf' as const,
            data: 'JVBERi0xLjQKMSAwIG9iago8PAovVHlwZSAvQ2F0YWxvZwovUGFnZXMgMiAwIFIKPj4KZW5kb2JqCg==',
            name: 'test-base64.pdf'
          }}
          onReady={onReadyBase64}
        />
      );

      await waitFor(() => {
        expect(onReadyBase64).toHaveBeenCalledTimes(1);
      }, { timeout: 5000 }); // Longer timeout for base64 conversion

      // Direct URI should be significantly faster
      expect(onReadyDirect).toHaveBeenCalledTimes(1);
      expect(onReadyBase64).toHaveBeenCalledTimes(1);
    });
  });

  describe('Optimization Detection', () => {
    it('should detect PDF files from DocumentPicker correctly', () => {
      // Test various DocumentPicker URI formats
      const testCases = [
        {
          uri: 'file:///data/user/0/app.rork.aipdfassistant/cache/DocumentPicker/test.pdf',
          shouldOptimize: true,
          description: 'Standard DocumentPicker PDF URI'
        },
        {
          uri: 'file:///storage/emulated/0/Download/document.pdf',
          shouldOptimize: true,
          description: 'Download folder PDF URI'
        },
        {
          uri: 'content://com.android.providers.downloads.documents/document/123',
          shouldOptimize: false,
          description: 'Content URI (not file URI)'
        },
        {
          uri: 'https://example.com/document.pdf',
          shouldOptimize: false,
          description: 'Remote PDF URL'
        }
      ];

      testCases.forEach(({ uri, shouldOptimize, description }) => {
        const source = {
          kind: 'pdf' as const,
          data: shouldOptimize ? '' : 'base64data',
          fileUri: shouldOptimize ? uri : undefined,
          name: 'test.pdf'
        };

        render(<PDFViewer source={source} />);

        if (shouldOptimize) {
          expect(console.log).toHaveBeenCalledWith(
            expect.stringContaining('[NativePDF] Using direct file URI (optimized path)')
          );
        } else {
          expect(console.log).toHaveBeenCalledWith(
            expect.stringContaining('[NativePDF] Using base64 conversion (legacy path)')
          );
        }

        console.log(`✅ ${description}: ${shouldOptimize ? 'Optimized' : 'Fallback'}`);
      });
    });
  });
});
